-- 期货相关表结构
-- 作者: system
-- 创建时间: 2024-01-01

-- 期货合约表
CREATE TABLE IF NOT EXISTS futures_contract (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '合约ID',
    symbol VARCHAR(50) NOT NULL UNIQUE COMMENT '合约符号',
    base_asset VARCHAR(20) NOT NULL COMMENT '基础资产',
    quote_asset VARCHAR(20) NOT NULL COMMENT '计价资产',
    contract_type VARCHAR(20) NOT NULL DEFAULT 'PERPETUAL' COMMENT '合约类型',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '合约状态',
    price_precision INT NOT NULL DEFAULT 8 COMMENT '价格精度',
    quantity_precision INT NOT NULL DEFAULT 8 COMMENT '数量精度',
    min_quantity DECIMAL(20,8) NOT NULL DEFAULT 0.001 COMMENT '最小下单数量',
    max_quantity DECIMAL(20,8) NOT NULL DEFAULT 1000000 COMMENT '最大下单数量',
    min_notional DECIMAL(20,8) NOT NULL DEFAULT 10 COMMENT '最小名义价值',
    max_notional DECIMAL(20,8) NOT NULL DEFAULT 10000000 COMMENT '最大名义价值',
    tick_size DECIMAL(20,8) NOT NULL DEFAULT 0.01 COMMENT '价格变动单位',
    step_size DECIMAL(20,8) NOT NULL DEFAULT 0.001 COMMENT '数量变动单位',
    maker_fee_rate DECIMAL(10,6) NOT NULL DEFAULT 0.0002 COMMENT 'Maker手续费率',
    taker_fee_rate DECIMAL(10,6) NOT NULL DEFAULT 0.0004 COMMENT 'Taker手续费率',
    max_leverage INT NOT NULL DEFAULT 100 COMMENT '最大杠杆倍数',
    maintenance_margin_rate DECIMAL(10,6) NOT NULL DEFAULT 0.005 COMMENT '维持保证金率',
    mark_price DECIMAL(20,8) COMMENT '标记价格',
    index_price DECIMAL(20,8) COMMENT '指数价格',
    funding_rate DECIMAL(10,8) COMMENT '资金费率',
    next_funding_time DATETIME COMMENT '下次资金费用时间',
    funding_interval INT NOT NULL DEFAULT 8 COMMENT '资金费用间隔（小时）',
    contract_size DECIMAL(20,8) NOT NULL DEFAULT 1 COMMENT '合约大小',
    is_hot TINYINT NOT NULL DEFAULT 0 COMMENT '是否热门合约',
    is_new TINYINT NOT NULL DEFAULT 0 COMMENT '是否新上线合约',
    volume_24h DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '24小时成交量',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货合约表';

-- 期货订单表
CREATE TABLE IF NOT EXISTS futures_order (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID',
    order_id VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    order_type VARCHAR(20) NOT NULL COMMENT '订单类型',
    side VARCHAR(10) NOT NULL COMMENT '买卖方向',
    quantity DECIMAL(20,8) NOT NULL COMMENT '订单数量',
    price DECIMAL(20,8) COMMENT '订单价格',
    stop_price DECIMAL(20,8) COMMENT '触发价格',
    executed_quantity DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '已成交数量',
    filled_quantity DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '已成交数量（别名）',
    executed_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '已成交金额',
    avg_price DECIMAL(20,8) COMMENT '平均成交价格',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
    time_in_force VARCHAR(10) NOT NULL DEFAULT 'GTC' COMMENT '有效期类型',
    leverage INT COMMENT '杠杆倍数',
    margin DECIMAL(20,8) COMMENT '保证金',
    fee DECIMAL(20,8) COMMENT '手续费',
    fee_currency VARCHAR(20) COMMENT '手续费币种',
    reduce_only TINYINT NOT NULL DEFAULT 0 COMMENT '是否只减仓',
    close_position TINYINT NOT NULL DEFAULT 0 COMMENT '是否平仓',
    client_order_id VARCHAR(50) COMMENT '客户端订单ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_order_id (order_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货订单表';

-- 期货持仓表
CREATE TABLE IF NOT EXISTS futures_position (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '持仓ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    quantity DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '持仓数量',
    avg_price DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '平均开仓价格',
    mark_price DECIMAL(20,8) COMMENT '标记价格',
    margin DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '保证金',
    unrealized_pnl DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '未实现盈亏',
    realized_pnl DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '已实现盈亏',
    leverage INT NOT NULL DEFAULT 1 COMMENT '杠杆倍数',
    margin_type VARCHAR(20) NOT NULL DEFAULT 'ISOLATED' COMMENT '保证金模式',
    position_side VARCHAR(10) NOT NULL DEFAULT 'BOTH' COMMENT '持仓方向',
    liquidation_price DECIMAL(20,8) COMMENT '强平价格',
    bankruptcy_price DECIMAL(20,8) COMMENT '破产价格',
    adl_quantile INT COMMENT 'ADL排队位置',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_user_symbol (user_id, symbol),
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货持仓表';

-- 期货成交记录表
CREATE TABLE IF NOT EXISTS futures_trade (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '成交ID',
    trade_id VARCHAR(50) NOT NULL UNIQUE COMMENT '成交号',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    buyer_order_id VARCHAR(50) NOT NULL COMMENT '买方订单ID',
    seller_order_id VARCHAR(50) NOT NULL COMMENT '卖方订单ID',
    buyer_user_id BIGINT NOT NULL COMMENT '买方用户ID',
    seller_user_id BIGINT NOT NULL COMMENT '卖方用户ID',
    price DECIMAL(20,8) NOT NULL COMMENT '成交价格',
    quantity DECIMAL(20,8) NOT NULL COMMENT '成交数量',
    amount DECIMAL(20,8) NOT NULL COMMENT '成交金额',
    side VARCHAR(10) NOT NULL COMMENT '成交方向',
    buyer_fee DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '买方手续费',
    seller_fee DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '卖方手续费',
    buyer_fee_currency VARCHAR(20) COMMENT '买方手续费币种',
    seller_fee_currency VARCHAR(20) COMMENT '卖方手续费币种',
    is_buyer_maker TINYINT NOT NULL DEFAULT 0 COMMENT '买方是否为挂单方',
    trade_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '成交时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_symbol (symbol),
    INDEX idx_buyer_user_id (buyer_user_id),
    INDEX idx_seller_user_id (seller_user_id),
    INDEX idx_trade_time (trade_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货成交记录表';

-- 期货保证金账户表
CREATE TABLE IF NOT EXISTS futures_margin_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    asset VARCHAR(20) NOT NULL COMMENT '资产类型',
    balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '总余额',
    available_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '可用余额',
    frozen_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '冻结余额',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_user_asset (user_id, asset),
    INDEX idx_user_id (user_id),
    INDEX idx_asset (asset)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货保证金账户表';

-- 现货账户表
CREATE TABLE IF NOT EXISTS spot_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    asset VARCHAR(20) NOT NULL COMMENT '资产类型',
    balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '总余额',
    available_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '可用余额',
    frozen_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '冻结余额',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_user_asset (user_id, asset),
    INDEX idx_user_id (user_id),
    INDEX idx_asset (asset)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='现货账户表';

-- 资产划转记录表
CREATE TABLE IF NOT EXISTS asset_transfer_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    asset VARCHAR(20) NOT NULL COMMENT '资产类型',
    amount DECIMAL(20,8) NOT NULL COMMENT '划转金额',
    type INT NOT NULL COMMENT '划转类型：1-现货到合约，2-合约到现货',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：1-成功，2-失败，3-处理中',
    remark VARCHAR(255) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_asset (asset),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产划转记录表';

-- 期货强平记录表
CREATE TABLE IF NOT EXISTS futures_liquidation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '强平ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    side VARCHAR(10) NOT NULL COMMENT '强平方向',
    liquidation_type VARCHAR(20) NOT NULL COMMENT '强平类型',
    quantity DECIMAL(20,8) NOT NULL COMMENT '强平数量',
    price DECIMAL(20,8) NOT NULL COMMENT '强平价格',
    amount DECIMAL(20,8) NOT NULL COMMENT '强平金额',
    fee DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '强平手续费',
    insurance_fund_used DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '使用的保险基金',
    bankruptcy_price DECIMAL(20,8) COMMENT '破产价格',
    margin_ratio DECIMAL(10,6) COMMENT '保证金率',
    mark_price DECIMAL(20,8) COMMENT '标记价格',
    realized_pnl DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '已实现盈亏',
    margin_released DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '释放的保证金',
    liquidation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '强平时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_liquidation_time (liquidation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货强平记录表';

-- 期货资金费率表
CREATE TABLE IF NOT EXISTS futures_funding_rate (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '费率ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    funding_rate DECIMAL(10,8) NOT NULL COMMENT '资金费率',
    mark_price DECIMAL(20,8) NOT NULL COMMENT '标记价格',
    index_price DECIMAL(20,8) NOT NULL COMMENT '指数价格',
    interest_rate DECIMAL(10,8) COMMENT '利率',
    funding_time DATETIME NOT NULL COMMENT '资金费用时间',
    next_funding_time DATETIME NOT NULL COMMENT '下次资金费用时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_symbol (symbol),
    INDEX idx_funding_time (funding_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货资金费率表';

-- 期货资金费用记录表
CREATE TABLE IF NOT EXISTS futures_funding_fee (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '费用ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    funding_rate DECIMAL(10,8) NOT NULL COMMENT '资金费率',
    position_size DECIMAL(20,8) NOT NULL COMMENT '持仓大小',
    funding_fee DECIMAL(20,8) NOT NULL COMMENT '资金费用',
    mark_price DECIMAL(20,8) NOT NULL COMMENT '标记价格',
    funding_time DATETIME NOT NULL COMMENT '资金费用时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_funding_time (funding_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货资金费用记录表';

-- 期货保险基金表
CREATE TABLE IF NOT EXISTS futures_insurance_fund (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '保险基金ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    asset VARCHAR(20) NOT NULL COMMENT '资产类型',
    balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '保险基金余额',
    total_income DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计收入',
    total_expense DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计支出',
    status INT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-暂停',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_symbol (symbol),
    INDEX idx_asset (asset),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货保险基金表';

-- 期货K线数据表
CREATE TABLE IF NOT EXISTS futures_kline (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'K线ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    interval VARCHAR(10) NOT NULL COMMENT '时间间隔',
    open_price DECIMAL(20,8) NOT NULL COMMENT '开盘价',
    high_price DECIMAL(20,8) NOT NULL COMMENT '最高价',
    low_price DECIMAL(20,8) NOT NULL COMMENT '最低价',
    close_price DECIMAL(20,8) NOT NULL COMMENT '收盘价',
    volume DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '成交量',
    amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '成交额',
    count BIGINT NOT NULL DEFAULT 0 COMMENT '成交笔数',
    open_time DATETIME NOT NULL COMMENT 'K线开始时间',
    close_time DATETIME NOT NULL COMMENT 'K线结束时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_symbol_interval_time (symbol, interval, open_time),
    INDEX idx_symbol (symbol),
    INDEX idx_interval (interval),
    INDEX idx_open_time (open_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货K线数据表';

-- 期货标记价格表
CREATE TABLE IF NOT EXISTS futures_mark_price (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '价格ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    mark_price DECIMAL(20,8) NOT NULL COMMENT '标记价格',
    index_price DECIMAL(20,8) COMMENT '指数价格',
    fair_price DECIMAL(20,8) COMMENT '公允价格',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_symbol (symbol),
    INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货标记价格表';

-- 期货指数价格表
CREATE TABLE IF NOT EXISTS futures_index_price (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '价格ID',
    symbol VARCHAR(50) NOT NULL COMMENT '合约符号',
    index_price DECIMAL(20,8) NOT NULL COMMENT '指数价格',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_symbol (symbol),
    INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期货指数价格表';

-- 保证金账户表
CREATE TABLE IF NOT EXISTS margin_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    symbol VARCHAR(50) COMMENT '币种符号',
    account_type TINYINT NOT NULL DEFAULT 1 COMMENT '账户类型：1-逐仓，2-全仓',
    contract_id BIGINT COMMENT '合约ID（逐仓时使用）',
    contract_symbol VARCHAR(50) COMMENT '合约符号（逐仓时使用）',
    base_currency_id BIGINT COMMENT '基础币种ID',
    quote_currency_id BIGINT COMMENT '计价币种ID',
    balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '总余额',
    available_margin DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '可用保证金',
    frozen_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '冻结余额',
    position_margin DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '持仓保证金',
    order_margin DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '委托保证金',
    unrealized_pnl DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '未实现盈亏',
    realized_pnl DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '已实现盈亏',
    total_wallet_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '钱包总余额',
    margin_ratio DECIMAL(10,6) COMMENT '保证金率',
    risk_level TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1-安全，2-警告，3-危险，4-强平',
    max_withdraw_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '最大可提取金额',
    last_update_time BIGINT NOT NULL DEFAULT 0 COMMENT '最后更新时间戳',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-冻结',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_symbol (symbol),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保证金账户表';

-- 用户钱包表
CREATE TABLE IF NOT EXISTS user_wallets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '钱包ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    currency VARCHAR(20) NOT NULL COMMENT '币种',
    wallet_type VARCHAR(20) NOT NULL DEFAULT 'SPOT' COMMENT '钱包类型：SPOT-现货，FUTURES-合约，MARGIN-杠杆',
    available_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '可用余额',
    frozen_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '冻结余额',
    total_balance DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '总余额',
    wallet_address VARCHAR(255) COMMENT '钱包地址',
    private_key VARCHAR(255) COMMENT '私钥（加密存储）',
    public_key VARCHAR(255) COMMENT '公钥',
    mnemonic VARCHAR(500) COMMENT '助记词（加密存储）',
    derivation_path VARCHAR(100) COMMENT '派生路径',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-冻结',
    is_main_wallet TINYINT NOT NULL DEFAULT 0 COMMENT '是否为主钱包：0-否，1-是',
    wallet_tag VARCHAR(50) COMMENT '钱包标签',
    last_transaction_time DATETIME COMMENT '最后交易时间',
    total_deposit_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计充值金额',
    total_withdraw_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计提现金额',
    total_trade_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计交易金额',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY uk_user_currency_type (user_id, currency, wallet_type),
    INDEX idx_user_id (user_id),
    INDEX idx_currency (currency),
    INDEX idx_wallet_type (wallet_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包表';
