-- 用户相关表结构
-- 作者: system
-- 创建时间: 2024-01-01

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(20) COMMENT '身份证号',
    birth_date DATE COMMENT '出生日期',
    gender TINYINT COMMENT '性别：0-未知，1-男，2-女',
    country VARCHAR(50) COMMENT '国家',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    address VARCHAR(255) COMMENT '详细地址',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-冻结',
    kyc_status TINYINT NOT NULL DEFAULT 0 COMMENT 'KYC状态：0-未认证，1-待审核，2-已认证，3-认证失败',
    kyc_level TINYINT NOT NULL DEFAULT 0 COMMENT 'KYC等级：0-未认证，1-初级，2-中级，3-高级',
    is_email_verified TINYINT NOT NULL DEFAULT 0 COMMENT '邮箱是否验证：0-未验证，1-已验证',
    is_phone_verified TINYINT NOT NULL DEFAULT 0 COMMENT '手机是否验证：0-未验证，1-已验证',
    is_two_factor_enabled TINYINT NOT NULL DEFAULT 0 COMMENT '是否启用双因子认证：0-未启用，1-已启用',
    two_factor_secret VARCHAR(32) COMMENT '双因子认证密钥',
    trade_password VARCHAR(255) COMMENT '交易密码（加密后）',
    trade_salt VARCHAR(32) COMMENT '交易密码盐值',
    fee_level INT NOT NULL DEFAULT 0 COMMENT '手续费等级：0-默认，1-VIP1，2-VIP2，3-VIP3',
    user_level VARCHAR(20) NOT NULL DEFAULT 'NORMAL' COMMENT '用户等级：NORMAL-普通用户，VIP-VIP用户，PREMIUM-高级用户',
    referrer_id BIGINT COMMENT '推荐人ID',
    referral_code VARCHAR(20) UNIQUE COMMENT '推荐码',
    total_trade_volume DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计交易量（USDT）',
    total_fee DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '累计手续费（USDT）',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    register_ip VARCHAR(45) COMMENT '注册IP',
    login_count INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建者',
    update_by VARCHAR(50) COMMENT '更新者',
    remark TEXT COMMENT '备注',
    freeze_reason VARCHAR(255) COMMENT '冻结原因',
    freeze_time DATETIME COMMENT '冻结时间',
    unfreeze_time DATETIME COMMENT '解冻时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referral_code (referral_code),
    INDEX idx_status (status),
    INDEX idx_kyc_status (kyc_status),
    INDEX idx_create_time (create_time),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户权限表
CREATE TABLE IF NOT EXISTS user_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    permission_type VARCHAR(50) NOT NULL COMMENT '权限类型',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_description TEXT COMMENT '权限描述',
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLED' COMMENT '权限状态：ENABLED-启用，DISABLED-禁用',
    daily_trading_limit DECIMAL(20,8) COMMENT '日交易限额',
    daily_withdrawal_limit DECIMAL(20,8) COMMENT '日提现限额',
    granted_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    granted_by VARCHAR(50) COMMENT '授权人',
    revoked_time DATETIME COMMENT '撤销时间',
    revoked_by VARCHAR(50) COMMENT '撤销人',
    expiry_time DATETIME COMMENT '过期时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type),
    INDEX idx_status (status),
    INDEX idx_granted_time (granted_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限表';

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type VARCHAR(50) NOT NULL COMMENT '通知类型',
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL' COMMENT '优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急',
    status VARCHAR(20) NOT NULL DEFAULT 'UNREAD' COMMENT '状态：UNREAD-未读，READ-已读，DELETED-已删除',
    is_read TINYINT NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    read_time DATETIME COMMENT '阅读时间',
    link_url VARCHAR(500) COMMENT '链接地址',
    sender_id BIGINT COMMENT '发送者ID',
    sender_type VARCHAR(20) COMMENT '发送者类型：SYSTEM-系统，ADMIN-管理员，USER-用户',
    business_id VARCHAR(100) COMMENT '业务ID',
    business_type VARCHAR(50) COMMENT '业务类型',
    expiry_time DATETIME COMMENT '过期时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_is_read (is_read),
    INDEX idx_priority (priority),
    INDEX idx_create_time (create_time),
    INDEX idx_business (business_type, business_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 签到记录表
CREATE TABLE IF NOT EXISTS check_in_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '签到记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    check_in_date DATE NOT NULL COMMENT '签到日期',
    check_in_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签到时间',
    continuous_days INT NOT NULL DEFAULT 1 COMMENT '连续签到天数',
    reward_type VARCHAR(20) NOT NULL COMMENT '奖励类型：COIN-代币，POINTS-积分',
    reward_amount DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '奖励数量',
    reward_currency VARCHAR(10) COMMENT '奖励币种',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    device_info VARCHAR(255) COMMENT '设备信息',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_date (user_id, check_in_date),
    INDEX idx_user_id (user_id),
    INDEX idx_check_in_date (check_in_date),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='签到记录表';

-- 推荐记录表
CREATE TABLE IF NOT EXISTS referral_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐记录ID',
    referrer_id BIGINT NOT NULL COMMENT '推荐人ID',
    referred_id BIGINT NOT NULL COMMENT '被推荐人ID',
    referral_code VARCHAR(20) NOT NULL COMMENT '推荐码',
    commission_rate DECIMAL(6,4) NOT NULL DEFAULT 0.2000 COMMENT '佣金率',
    total_commission DECIMAL(20,8) NOT NULL DEFAULT 0 COMMENT '总佣金',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-活跃，INACTIVE-非活跃',
    first_trade_time DATETIME COMMENT '首次交易时间',
    last_trade_time DATETIME COMMENT '最后交易时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_referred (referred_id),
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referral_code (referral_code),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推荐记录表';

-- 登录历史表
CREATE TABLE IF NOT EXISTS login_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '登录历史ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    login_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    logout_time DATETIME COMMENT '登出时间',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    device_type VARCHAR(20) COMMENT '设备类型：WEB-网页，MOBILE-手机，TABLET-平板',
    device_info VARCHAR(255) COMMENT '设备信息',
    location VARCHAR(100) COMMENT '登录地点',
    country VARCHAR(50) COMMENT '国家',
    city VARCHAR(50) COMMENT '城市',
    login_status VARCHAR(20) NOT NULL DEFAULT 'SUCCESS' COMMENT '登录状态：SUCCESS-成功，FAILED-失败',
    failure_reason VARCHAR(255) COMMENT '失败原因',
    session_id VARCHAR(100) COMMENT '会话ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    INDEX idx_ip_address (ip_address),
    INDEX idx_login_status (login_status),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录历史表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '操作日志ID',
    user_id BIGINT COMMENT '用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_description VARCHAR(255) NOT NULL COMMENT '操作描述',
    operation_details TEXT COMMENT '操作详情',
    operation_result VARCHAR(20) NOT NULL COMMENT '操作结果：SUCCESS-成功，FAILED-失败',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_id VARCHAR(100) COMMENT '请求ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_time (operation_time),
    INDEX idx_operation_result (operation_result),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
