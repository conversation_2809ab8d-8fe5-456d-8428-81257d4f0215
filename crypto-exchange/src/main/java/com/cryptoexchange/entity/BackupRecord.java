package com.cryptoexchange.entity;

import com.cryptoexchange.enums.BackupStatus;
import com.cryptoexchange.enums.BackupType;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BackupRecord {
    private Long id;
    private String backupName;
    private String backupPath;
    private LocalDateTime backupTime;
    private BackupStatus status;
    private BackupType backupType;
    private String remark;
    private String remoteStorageStatus;
    private String remoteStorageUrl;
}