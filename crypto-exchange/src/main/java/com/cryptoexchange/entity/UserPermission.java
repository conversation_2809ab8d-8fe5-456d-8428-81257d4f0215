package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户权限实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_permissions")
public class UserPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 权限类型：TRADING-交易权限，WITHDRAWAL-提现权限，API-API权限，FUTURES-合约权限
     */
    @TableField("permission_type")
    private String permissionType;

    /**
     * 权限状态：ENABLED-启用，DISABLED-禁用，SUSPENDED-暂停
     */
    @TableField("status")
    private String status;

    /**
     * 每日交易限额
     */
    @TableField("daily_trading_limit")
    private BigDecimal dailyTradingLimit;

    /**
     * 每日提现限额
     */
    @TableField("daily_withdrawal_limit")
    private BigDecimal dailyWithdrawalLimit;

    /**
     * 单笔交易限额
     */
    @TableField("single_trade_limit")
    private BigDecimal singleTradeLimit;

    /**
     * 单笔提现限额
     */
    @TableField("single_withdrawal_limit")
    private BigDecimal singleWithdrawalLimit;

    /**
     * API调用频率限制（次/分钟）
     */
    @TableField("api_rate_limit")
    private Integer apiRateLimit;

    /**
     * 合约最大杠杆倍数
     */
    @TableField("max_leverage")
    private Integer maxLeverage;

    /**
     * 权限描述
     */
    @TableField("description")
    private String description;

    /**
     * 权限生效时间
     */
    @TableField("effective_time")
    private LocalDateTime effectiveTime;

    /**
     * 权限过期时间
     */
    @TableField("expiry_time")
    private LocalDateTime expiryTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}