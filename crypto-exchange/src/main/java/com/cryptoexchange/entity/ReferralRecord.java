package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推荐记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("referral_records")
public class ReferralRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 推荐记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 推荐人用户ID
     */
    @TableField("referrer_user_id")
    private Long referrerUserId;

    /**
     * 被推荐人用户ID
     */
    @TableField("referred_user_id")
    private Long referredUserId;

    /**
     * 推荐码
     */
    @TableField("referral_code")
    private String referralCode;

    /**
     * 推荐状态：PENDING-待激活，ACTIVE-已激活，EXPIRED-已过期
     */
    @TableField("status")
    private String status;

    /**
     * 推荐等级：1-一级推荐，2-二级推荐
     */
    @TableField("referral_level")
    private Integer referralLevel;

    /**
     * 累计佣金收益
     */
    @TableField("total_commission")
    private BigDecimal totalCommission;

    /**
     * 佣金币种
     */
    @TableField("commission_currency")
    private String commissionCurrency;

    /**
     * 被推荐人累计交易量
     */
    @TableField("referred_user_volume")
    private BigDecimal referredUserVolume;

    /**
     * 激活时间
     */
    @TableField("activation_time")
    private LocalDateTime activationTime;

    /**
     * 过期时间
     */
    @TableField("expiry_time")
    private LocalDateTime expiryTime;

    /**
     * 推荐来源：WEB-网页，APP-应用，API-接口
     */
    @TableField("referral_source")
    private String referralSource;

    /**
     * 推荐IP地址
     */
    @TableField("referral_ip")
    private String referralIp;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}