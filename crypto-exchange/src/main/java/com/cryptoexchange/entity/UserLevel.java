package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户等级实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_levels")
public class UserLevel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户等级ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 当前等级：1-青铜，2-白银，3-黄金，4-铂金，5-钻石，6-王者
     */
    @TableField("current_level")
    private Integer currentLevel;

    /**
     * 当前经验值
     */
    @TableField("current_exp")
    private BigDecimal currentExp;

    /**
     * 升级所需经验值
     */
    @TableField("required_exp")
    private BigDecimal requiredExp;

    /**
     * 累计交易量（USDT）
     */
    @TableField("total_trading_volume")
    private BigDecimal totalTradingVolume;

    /**
     * 累计手续费（USDT）
     */
    @TableField("total_fees_paid")
    private BigDecimal totalFeesPaid;

    /**
     * 推荐用户数量
     */
    @TableField("referral_count")
    private Integer referralCount;

    /**
     * 持有平台币数量
     */
    @TableField("platform_token_balance")
    private BigDecimal platformTokenBalance;

    /**
     * KYC状态：0-未认证，1-已认证
     */
    @TableField("kyc_status")
    private Integer kycStatus;

    /**
     * 账户年龄（天）
     */
    @TableField("account_age_days")
    private Integer accountAgeDays;

    /**
     * 上次等级更新时间
     */
    @TableField("last_level_update")
    private LocalDateTime lastLevelUpdate;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}