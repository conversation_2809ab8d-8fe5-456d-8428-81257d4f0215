package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货K线数据实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_kline")
public class FuturesKline implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * K线ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 时间间隔
     */
    @TableField("interval")
    private String interval;

    /**
     * 开盘价
     */
    @TableField("open_price")
    private BigDecimal openPrice;

    /**
     * 最高价
     */
    @TableField("high_price")
    private BigDecimal highPrice;

    /**
     * 最低价
     */
    @TableField("low_price")
    private BigDecimal lowPrice;

    /**
     * 收盘价
     */
    @TableField("close_price")
    private BigDecimal closePrice;

    /**
     * 成交量
     */
    @TableField("volume")
    private BigDecimal volume;

    /**
     * 成交额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 成交笔数
     */
    @TableField("count")
    private Long count;

    /**
     * K线开始时间
     */
    @TableField("open_time")
    private LocalDateTime openTime;

    /**
     * K线结束时间
     */
    @TableField("close_time")
    private LocalDateTime closeTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}
