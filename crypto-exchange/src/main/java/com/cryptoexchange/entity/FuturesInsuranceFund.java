package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货保险基金实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_insurance_fund")
public class FuturesInsuranceFund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 保险基金ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 资产类型
     */
    @TableField("asset")
    private String asset;

    /**
     * 保险基金余额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 累计收入
     */
    @TableField("total_income")
    private BigDecimal totalIncome;

    /**
     * 累计支出
     */
    @TableField("total_expense")
    private BigDecimal totalExpense;

    /**
     * 状态：1-正常，2-暂停
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}
