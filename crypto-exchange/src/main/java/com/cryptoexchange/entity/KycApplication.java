package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * KYC申请实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("kyc_application")
public class KycApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 全名（别名）
     */
    @TableField("full_name")
    private String fullName;

    /**
     * 身份证号
     */
    @TableField("id_number")
    private String idNumber;

    /**
     * 证件类型
     */
    @TableField("id_type")
    private String idType;

    /**
     * 出生日期
     */
    @TableField("date_of_birth")
    private String dateOfBirth;

    /**
     * 身份证正面照片URL
     */
    @TableField("id_card_front_url")
    private String idCardFrontUrl;

    /**
     * 身份证反面照片URL
     */
    @TableField("id_card_back_url")
    private String idCardBackUrl;

    /**
     * 手持身份证照片URL
     */
    @TableField("id_card_hand_url")
    private String idCardHandUrl;

    /**
     * 国籍
     */
    @TableField("nationality")
    private String nationality;

    /**
     * 职业
     */
    @TableField("occupation")
    private String occupation;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 邮政编码
     */
    @TableField("postal_code")
    private String postalCode;

    /**
     * 收入来源
     */
    @TableField("income_source")
    private String incomeSource;

    /**
     * 身份证正面图片
     */
    @TableField("id_front_image")
    private String idFrontImage;

    /**
     * 身份证背面图片
     */
    @TableField("id_back_image")
    private String idBackImage;

    /**
     * 自拍照片
     */
    @TableField("selfie_image")
    private String selfieImage;

    /**
     * 地址证明图片
     */
    @TableField("proof_of_address_image")
    private String proofOfAddressImage;

    /**
     * 申请类型：INDIVIDUAL-个人认证，ENTERPRISE-企业认证
     */
    @TableField("application_type")
    private String applicationType;

    /**
     * 申请状态：0-待审核，1-审核中，2-审核通过，3-审核拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * 审核备注
     */
    @TableField("review_remark")
    private String reviewRemark;

    /**
     * 审核人ID
     */
    @TableField("reviewer_id")
    private Long reviewerId;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}