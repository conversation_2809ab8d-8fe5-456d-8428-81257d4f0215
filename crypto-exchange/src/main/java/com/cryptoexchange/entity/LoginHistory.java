package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户登录历史实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("login_history")
public class LoginHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 登录历史ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 登录IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 登录地点
     */
    @TableField("location")
    private String location;

    /**
     * 设备类型：WEB-网页，MOBILE-手机，TABLET-平板，DESKTOP-桌面
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 设备信息
     */
    @TableField("device_info")
    private String deviceInfo;

    /**
     * 浏览器信息
     */
    @TableField("browser_info")
    private String browserInfo;

    /**
     * 浏览器（别名）
     */
    @TableField("browser")
    private String browser;

    /**
     * 登录方式
     */
    @TableField("login_method")
    private String loginMethod;

    /**
     * 操作系统
     */
    @TableField("operating_system")
    private String operatingSystem;

    /**
     * 登录状态：SUCCESS-成功，FAILED-失败，BLOCKED-被阻止
     */
    @TableField("login_status")
    private String loginStatus;

    /**
     * 失败原因（如果登录失败）
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 登录时间
     */
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 登出时间
     */
    @TableField("logout_time")
    private LocalDateTime logoutTime;

    /**
     * 会话持续时间（分钟）
     */
    @TableField("session_duration")
    private Integer sessionDuration;

    /**
     * 是否可疑登录：0-正常，1-可疑
     */
    @TableField("is_suspicious")
    private Integer isSuspicious;
    
    /**
     * 设备标识
     */
    @TableField("device")
    private String device;
    
    /**
     * 是否当前会话：0-否，1-是
     */
    @TableField("is_current_session")
    private Integer isCurrentSession;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}