package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货强平记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_liquidation")
public class FuturesLiquidation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 强平记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 强平编号
     */
    @TableField("liquidation_id")
    private String liquidationId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 强平方向：BUY-买入强平，SELL-卖出强平
     */
    @TableField("side")
    private String side;

    /**
     * 强平数量
     */
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 强平价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 强平金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 强平手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 已实现盈亏
     */
    @TableField("realized_pnl")
    private BigDecimal realizedPnl;

    /**
     * 释放的保证金
     */
    @TableField("margin_released")
    private BigDecimal marginReleased;

    /**
     * 强平类型：FORCED-强制强平，ADL-自动减仓
     */
    @TableField("liquidation_type")
    private String liquidationType;

    /**
     * 强平原因
     */
    @TableField("liquidation_reason")
    private String liquidationReason;

    /**
     * 风险率
     */
    @TableField("risk_ratio")
    private BigDecimal riskRatio;

    /**
     * 标记价格
     */
    @TableField("mark_price")
    private BigDecimal markPrice;

    /**
     * 破产价格
     */
    @TableField("bankruptcy_price")
    private BigDecimal bankruptcyPrice;

    /**
     * 强平状态：1-强平中，2-强平完成，3-强平失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}
