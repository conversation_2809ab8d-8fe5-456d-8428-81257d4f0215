package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户签到记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("check_in_records")
public class CheckInRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 签到记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 签到日期
     */
    @TableField("check_in_date")
    private LocalDate checkInDate;

    /**
     * 连续签到天数
     */
    @TableField("continuous_days")
    private Integer continuousDays;

    /**
     * 奖励类型：POINTS-积分，COIN-代币，COUPON-优惠券
     */
    @TableField("reward_type")
    private String rewardType;

    /**
     * 奖励数量
     */
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    /**
     * 奖励币种（如果是代币奖励）
     */
    @TableField("reward_currency")
    private String rewardCurrency;

    /**
     * 签到IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 设备信息
     */
    @TableField("device_info")
    private String deviceInfo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}