package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货持仓记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_position")
public class FuturesPosition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 持仓ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 持仓方向：1-多头，2-空头
     */
    @TableField("side")
    private Integer side;

    /**
     * 持仓数量
     */
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 可平仓数量
     */
    @TableField("available_quantity")
    private BigDecimal availableQuantity;

    /**
     * 平均开仓价格
     */
    @TableField("avg_price")
    private BigDecimal avgPrice;

    /**
     * 标记价格
     */
    @TableField("mark_price")
    private BigDecimal markPrice;

    /**
     * 强平价格
     */
    @TableField("liquidation_price")
    private BigDecimal liquidationPrice;

    /**
     * 保证金
     */
    @TableField("margin")
    private BigDecimal margin;

    /**
     * 初始保证金
     */
    @TableField("initial_margin")
    private BigDecimal initialMargin;

    /**
     * 维持保证金
     */
    @TableField("maintenance_margin")
    private BigDecimal maintenanceMargin;

    /**
     * 未实现盈亏
     */
    @TableField("unrealized_pnl")
    private BigDecimal unrealizedPnl;

    /**
     * 已实现盈亏
     */
    @TableField("realized_pnl")
    private BigDecimal realizedPnl;

    /**
     * 收益率
     */
    @TableField("roe")
    private BigDecimal roe;

    /**
     * 杠杆倍数
     */
    @TableField("leverage")
    private Integer leverage;

    /**
     * 保证金率
     */
    @TableField("margin_ratio")
    private BigDecimal marginRatio;

    /**
     * 持仓状态：1-正常，2-强平中，3-已平仓
     */
    @TableField("status")
    private Integer status;

    /**
     * 自动追加保证金
     */
    @TableField("auto_add_margin")
    private Boolean autoAddMargin;

    /**
     * 最后更新价格时间
     */
    @TableField("last_price_update_time")
    private LocalDateTime lastPriceUpdateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}