package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("users")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码（加密后）
     */
    @TableField("password")
    private String password;

    /**
     * 盐值
     */
    @TableField("salt")
    private String salt;

    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 头像URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 用户状态：0-禁用，1-正常，2-锁定
     */
    @TableField("status")
    private Integer status;

    /**
     * 用户类型：0-普通用户，1-VIP用户，2-机构用户
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 实名认证状态：0-未认证，1-待审核，2-已认证，3-认证失败
     */
    @TableField("kyc_status")
    private Integer kycStatus;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 国家/地区
     */
    @TableField("country")
    private String country;

    /**
     * 语言偏好
     */
    @TableField("language")
    private String language;

    /**
     * 时区
     */
    @TableField("timezone")
    private String timezone;

    /**
     * 是否启用两步验证
     */
    @TableField("two_factor_enabled")
    private Boolean twoFactorEnabled;

    /**
     * 两步验证密钥
     */
    @TableField("two_factor_secret")
    private String twoFactorSecret;

    /**
     * 交易密码（加密后）
     */
    @TableField("trade_password")
    private String tradePassword;

    /**
     * 交易密码盐值
     */
    @TableField("trade_salt")
    private String tradeSalt;

    /**
     * 手续费等级：0-默认，1-VIP1，2-VIP2，3-VIP3
     */
    @TableField("fee_level")
    private Integer feeLevel;

    /**
     * 用户等级：NORMAL-普通用户，VIP-VIP用户，PREMIUM-高级用户
     */
    @TableField("user_level")
    private String userLevel;

    /**
     * 推荐人ID
     */
    @TableField("referrer_id")
    private Long referrerId;

    /**
     * 推荐码
     */
    @TableField("referral_code")
    private String referralCode;

    /**
     * 累计交易量（USDT）
     */
    @TableField("total_trade_volume")
    private BigDecimal totalTradeVolume;

    /**
     * 累计手续费（USDT）
     */
    @TableField("total_fee")
    private BigDecimal totalFee;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 注册IP
     */
    @TableField("register_ip")
    private String registerIp;

    /**
     * 登录次数
     */
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 冻结原因
     */
    @TableField("freeze_reason")
    private String freezeReason;

    /**
     * 冻结时间
     */
    @TableField("freeze_time")
    private LocalDateTime freezeTime;

    /**
     * 解冻时间
     */
    @TableField("unfreeze_time")
    private LocalDateTime unfreezeTime;
}