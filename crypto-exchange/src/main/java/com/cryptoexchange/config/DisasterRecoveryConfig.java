package com.cryptoexchange.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 灾难恢复配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "disaster.recovery")
public class DisasterRecoveryConfig {
    
    /**
     * 是否启用自动备份
     */
    private boolean autoBackupEnabled = true;
    
    /**
     * 是否启用远程备份
     */
    private boolean remoteBackupEnabled = false;
    
    /**
     * 备份基础路径
     */
    private String backupBasePath = "/data/backup";
    
    /**
     * 配置文件路径
     */
    private String configPath = "/app/config";
    
    /**
     * 日志文件路径
     */
    private String logPath = "/app/logs";
    
    /**
     * 备份保留天数
     */
    private int backupRetentionDays = 30;

    /**
     * 备份间隔小时数
     */
    private int backupIntervalHours = 6;

    /**
     * 日志保留天数
     */
    private int logRetentionDays = 7;
    
    /**
     * 远程存储配置
     */
    private RemoteStorage remoteStorage = new RemoteStorage();
    
    /**
     * 数据库备份配置
     */
    private DatabaseBackup databaseBackup = new DatabaseBackup();
    
    /**
     * Redis备份配置
     */
    private RedisBackup redisBackup = new RedisBackup();
    
    /**
     * 容灾切换配置
     */
    private Failover failover = new Failover();
    
    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();
    
    @Data
    public static class RemoteStorage {
        /**
         * 存储类型 (s3, oss, cos等)
         */
        private String type = "s3";
        
        /**
         * 存储桶名称
         */
        private String bucketName;
        
        /**
         * 访问密钥ID
         */
        private String accessKeyId;
        
        /**
         * 访问密钥
         */
        private String accessKeySecret;
        
        /**
         * 区域
         */
        private String region;
        
        /**
         * 端点URL
         */
        private String endpoint;
        
        /**
         * 备份前缀
         */
        private String backupPrefix = "crypto-exchange/backup";
    }
    
    @Data
    public static class DatabaseBackup {
        /**
         * 数据库主机
         */
        private String host = "localhost";
        
        /**
         * 数据库端口
         */
        private int port = 3306;
        
        /**
         * 数据库名称
         */
        private String database = "crypto_exchange";
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码
         */
        private String password;
        
        /**
         * 备份工具路径
         */
        private String mysqldumpPath = "/usr/bin/mysqldump";
        
        /**
         * 恢复工具路径
         */
        private String mysqlPath = "/usr/bin/mysql";
        
        /**
         * 备份选项
         */
        private String backupOptions = "--single-transaction --routines --triggers";
        
        /**
         * 是否压缩备份文件
         */
        private boolean compressBackup = true;
        
        /**
         * 并行备份线程数
         */
        private int parallelThreads = 4;
    }
    
    @Data
    public static class RedisBackup {
        /**
         * Redis主机
         */
        private String host = "localhost";
        
        /**
         * Redis端口
         */
        private int port = 6379;
        
        /**
         * Redis密码
         */
        private String password;
        
        /**
         * Redis数据目录
         */
        private String dataDir = "/var/lib/redis";
        
        /**
         * RDB文件名
         */
        private String rdbFileName = "dump.rdb";
        
        /**
         * AOF文件名
         */
        private String aofFileName = "appendonly.aof";
        
        /**
         * 是否备份RDB文件
         */
        private boolean backupRdb = true;
        
        /**
         * 是否备份AOF文件
         */
        private boolean backupAof = true;
    }
    
    @Data
    public static class Failover {
        /**
         * 主数据中心
         */
        private String primaryDataCenter = "dc1";
        
        /**
         * 备用数据中心列表
         */
        private String[] backupDataCenters = {"dc2", "dc3"};
        
        /**
         * 健康检查间隔（秒）
         */
        private int healthCheckInterval = 30;
        
        /**
         * 故障检测阈值
         */
        private int failureThreshold = 3;
        
        /**
         * 自动切换是否启用
         */
        private boolean autoFailoverEnabled = false;
        
        /**
         * 切换超时时间（秒）
         */
        private int failoverTimeout = 300;
        
        /**
         * DNS更新配置
         */
        private DnsConfig dnsConfig = new DnsConfig();
        
        @Data
        public static class DnsConfig {
            /**
             * DNS提供商 (cloudflare, route53, aliyun等)
             */
            private String provider = "cloudflare";
            
            /**
             * API密钥
             */
            private String apiKey;
            
            /**
             * API密钥ID
             */
            private String apiKeyId;
            
            /**
             * 域名
             */
            private String domain;
            
            /**
             * 记录名称
             */
            private String recordName = "api";
            
            /**
             * TTL
             */
            private int ttl = 60;
        }
    }
    
    @Data
    public static class Monitoring {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;
        
        /**
         * 监控间隔（秒）
         */
        private int interval = 60;
        
        /**
         * 告警配置
         */
        private AlertConfig alertConfig = new AlertConfig();
        
        @Data
        public static class AlertConfig {
            /**
             * 是否启用邮件告警
             */
            private boolean emailEnabled = true;
            
            /**
             * 是否启用短信告警
             */
            private boolean smsEnabled = false;
            
            /**
             * 是否启用钉钉告警
             */
            private boolean dingTalkEnabled = false;
            
            /**
             * 是否启用企业微信告警
             */
            private boolean wechatWorkEnabled = false;
            
            /**
             * 告警接收人邮箱列表
             */
            private String[] emailRecipients;
            
            /**
             * 告警接收人手机号列表
             */
            private String[] phoneRecipients;
            
            /**
             * 钉钉机器人Webhook
             */
            private String dingTalkWebhook;
            
            /**
             * 企业微信机器人Webhook
             */
            private String wechatWorkWebhook;
        }
    }
}