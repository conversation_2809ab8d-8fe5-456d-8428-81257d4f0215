package com.cryptoexchange.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.AdminUserQueryRequest;
import com.cryptoexchange.dto.request.KycReviewRequest;
import com.cryptoexchange.dto.request.UserNotificationQueryRequest;
import com.cryptoexchange.dto.response.AdminUserResponse;
import com.cryptoexchange.dto.response.CheckInRecordResponse;
import com.cryptoexchange.dto.response.CheckInResponse;
import com.cryptoexchange.dto.response.UnreadNotificationCountResponse;
import com.cryptoexchange.dto.response.UserNotificationResponse;
import com.cryptoexchange.dto.response.UserStatisticsResponse;
import com.cryptoexchange.dto.response.UserTradingOverviewResponse;
import com.cryptoexchange.dto.response.UserAssetOverviewResponse;
import com.cryptoexchange.dto.response.UserPermissionsResponse;
import com.cryptoexchange.dto.response.ReferralUserResponse;
import com.cryptoexchange.dto.response.ReferralInfoResponse;
import com.cryptoexchange.dto.response.UserLevelResponse;
import com.cryptoexchange.dto.response.LoginHistoryResponse;
import com.cryptoexchange.dto.response.AvatarUploadResponse;
import com.cryptoexchange.dto.response.KycStatusResponse;
import com.cryptoexchange.dto.response.SecuritySettingsResponse;
import com.cryptoexchange.dto.response.UserProfileResponse;
import com.cryptoexchange.dto.request.SetTradingPasswordRequest;
import com.cryptoexchange.dto.request.ChangePasswordRequest;
import com.cryptoexchange.dto.request.UpdateUserProfileRequest;
import com.cryptoexchange.dto.request.UpdateSecuritySettingsRequest;
import com.cryptoexchange.dto.request.KycApplicationRequest;
import com.cryptoexchange.dto.request.RealNameAuthRequest;
import com.cryptoexchange.dto.request.OperationLogQueryRequest;
import com.cryptoexchange.dto.request.SetupTwoFactorAuthRequest;
import com.cryptoexchange.dto.request.BindPhoneRequest;
import com.cryptoexchange.dto.request.BindEmailRequest;
import com.cryptoexchange.dto.response.TwoFactorAuthResponse;
import com.cryptoexchange.dto.response.OperationLogResponse;
import org.springframework.web.multipart.MultipartFile;
import com.cryptoexchange.entity.User;
import com.cryptoexchange.entity.KycApplication;
import com.cryptoexchange.entity.OperationLog;
import com.cryptoexchange.entity.Notification;
import com.cryptoexchange.entity.CheckInRecord;
import com.cryptoexchange.entity.LoginHistory;
import com.cryptoexchange.entity.UserLevel;
import com.cryptoexchange.entity.ReferralRecord;
import com.cryptoexchange.entity.UserPermission;
import com.cryptoexchange.exception.BusinessException;
import com.cryptoexchange.mapper.UserMapper;
import com.cryptoexchange.mapper.KycApplicationMapper;
import com.cryptoexchange.mapper.OperationLogMapper;
import com.cryptoexchange.mapper.NotificationMapper;
import com.cryptoexchange.mapper.CheckInMapper;
import com.cryptoexchange.mapper.LoginHistoryMapper;
import com.cryptoexchange.mapper.UserLevelMapper;
import com.cryptoexchange.mapper.ReferralMapper;
import com.cryptoexchange.mapper.UserPermissionMapper;
import com.cryptoexchange.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    private final UserMapper userMapper;
    private final KycApplicationMapper kycApplicationMapper;
    private final OperationLogMapper operationLogMapper;
    private final NotificationMapper notificationMapper;
    private final CheckInMapper checkInMapper;
    private final LoginHistoryMapper loginHistoryMapper;
    private final UserLevelMapper userLevelMapper;
    private final ReferralMapper referralMapper;
    private final UserPermissionMapper userPermissionMapper;
    private final PasswordEncoder passwordEncoder;
    
    @Value("${file.upload.path:/uploads/avatars/}")
    private String uploadPath;
    
    @Value("${file.upload.base-url:http://localhost:8080}")
    private String baseUrl;

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    public User findByReferralCode(String referralCode) {
        return userMapper.findByReferralCode(referralCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (existsByEmail(user.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }
        
        // 检查手机号是否已存在（如果提供）
        if (StringUtils.hasText(user.getPhone()) && existsByPhone(user.getPhone())) {
            throw new BusinessException("手机号已存在");
        }
        
        // 生成唯一推荐码
        if (!StringUtils.hasText(user.getReferralCode())) {
            user.setReferralCode(generateUniqueReferralCode());
        }
        
        // 设置默认值
        user.setStatus(1); // 正常状态
        user.setUserType(0); // 普通用户
        user.setKycStatus(0); // 未认证
        user.setTwoFactorEnabled(false);
        user.setFeeLevel(0);
        user.setTotalTradeVolume(BigDecimal.ZERO);
        user.setDeleted(0);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        // 保存用户
        save(user);
        
        log.info("用户创建成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(User user) {
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户信息更新成功: {}", user.getId());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, Integer status) {
        int result = userMapper.updateUserStatus(userId, status, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或状态更新失败");
        }
        log.info("用户状态更新成功: userId={}, status={}", userId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long userId, String newPassword) {
        String encodedPassword = passwordEncoder.encode(newPassword);
        int result = userMapper.updatePassword(userId, encodedPassword, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或密码更新失败");
        }
        log.info("用户密码更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTradingPassword(Long userId, String tradingPassword) {
        String encodedPassword = passwordEncoder.encode(tradingPassword);
        int result = userMapper.updateTradingPassword(userId, encodedPassword, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或交易密码更新失败");
        }
        log.info("用户交易密码更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AvatarUploadResponse uploadAvatar(Long userId, MultipartFile file) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证文件
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new BusinessException("只支持图片文件上传");
        }

        // 验证文件大小 (限制为5MB)
        long maxSize = 5 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            throw new BusinessException("文件大小不能超过5MB");
        }

        try {
            // 创建上传目录
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fullUploadPath = uploadPath + datePath;
            Path uploadDir = Paths.get(fullUploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String fileName = UUID.randomUUID().toString() + fileExtension;
            
            // 保存文件
            Path filePath = uploadDir.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);
            
            // 生成访问URL
            String relativePath = "/uploads/avatars/" + datePath + "/" + fileName;
            String avatarUrl = baseUrl + relativePath;
            
            // 删除旧头像文件（如果存在）
            if (StringUtils.hasText(user.getAvatar()) && user.getAvatar().startsWith(baseUrl)) {
                deleteOldAvatar(user.getAvatar());
            }

            // 更新用户头像
            user.setAvatar(avatarUrl);
            user.setUpdateTime(LocalDateTime.now());
            updateById(user);
            
            log.info("用户头像上传成功: userId={}, fileName={}, size={}", userId, fileName, file.getSize());
            
            return new AvatarUploadResponse(avatarUrl, fileName, file.getSize(), contentType);
            
        } catch (IOException e) {
            log.error("文件上传失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNickname(Long userId, String nickname) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setNickname(nickname);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户昵称更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLanguage(Long userId, String language) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setLanguage(language);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户语言偏好更新成功: userId={}, language={}", userId, language);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTimezone(Long userId, String timezone) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setTimezone(timezone);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户时区更新成功: userId={}, timezone={}", userId, timezone);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTwoFactorAuth(Long userId, Boolean enabled) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setTwoFactorEnabled(enabled);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户两步验证更新成功: userId={}, enabled={}", userId, enabled);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateKycStatus(Long userId, Integer kycStatus) {
        int result = userMapper.updateKycStatus(userId, kycStatus, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或KYC状态更新失败");
        }
        log.info("用户KYC状态更新成功: userId={}, kycStatus={}", userId, kycStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRealNameInfo(Long userId, String realName, String idNumber) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setRealName(realName);
        user.setIdCard(idNumber);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户实名信息更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFeeLevel(Long userId, Integer feeLevel) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setFeeLevel(feeLevel);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户手续费等级更新成功: userId={}, feeLevel={}", userId, feeLevel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTradingVolume(Long userId, BigDecimal volume) {
        int result = userMapper.updateTradingVolume(userId, volume, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或交易量更新失败");
        }
        log.info("用户交易量更新成功: userId={}, volume={}", userId, volume);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginInfo(Long userId, String loginIp, String userAgent) {
        int result = userMapper.updateLastLoginInfo(userId, loginIp, LocalDateTime.now(), userAgent);
        if (result == 0) {
            throw new BusinessException("用户不存在或登录信息更新失败");
        }
        log.info("用户登录信息更新成功: userId={}, loginIp={}", userId, loginIp);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.countByUsername(username) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.countByEmail(email) > 0;
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userMapper.countByPhone(phone) > 0;
    }

    @Override
    public boolean existsByReferralCode(String referralCode) {
        return userMapper.countByReferralCode(referralCode) > 0;
    }

    @Override
    public boolean userExists(Long userId) {
        return userMapper.existsById(userId);
    }

    @Override
    public String generateUniqueReferralCode() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        String referralCode;
        
        do {
            StringBuilder sb = new StringBuilder(6);
            for (int i = 0; i < 6; i++) {
                sb.append(characters.charAt(random.nextInt(characters.length())));
            }
            referralCode = sb.toString();
        } while (existsByReferralCode(referralCode));
        
        return referralCode;
    }

    @Override
    public PageResult<User> findUsers(Integer page, Integer size, String keyword, 
                                     Integer status, Integer userType, Integer kycStatus) {
        Page<User> pageObj = new Page<>(page, size);
        IPage<User> result = userMapper.findUsers(pageObj, keyword, status, userType, kycStatus);
        return PageResult.of(result);
    }

    @Override
    public PageResult<User> findReferralUsers(Long userId, Integer page, Integer size) {
        Page<User> pageObj = new Page<>(page, size);
        IPage<User> result = userMapper.findReferralUsers(pageObj, userId);
        return PageResult.of(result);
    }

    @Override
    public Map<String, Object> getUserStatistics(Long userId) {
        Map<String, Object> statistics = userMapper.getUserStatistics(userId);
        if (statistics == null) {
            statistics = new HashMap<>();
        }
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void softDeleteUser(Long userId) {
        int result = userMapper.softDeleteUser(userId, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或删除失败");
        }
        log.info("用户软删除成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreUser(Long userId) {
        int result = userMapper.restoreUser(userId, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或恢复失败");
        }
        log.info("用户恢复成功: userId={}", userId);
    }

    @Override
    public boolean verifyPassword(Long userId, String rawPassword) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    @Override
    public boolean verifyTradingPassword(Long userId, String rawTradingPassword) {
        User user = getById(userId);
        if (user == null || user.getTradePassword() == null) {
            return false;
        }
        return passwordEncoder.matches(rawTradingPassword, user.getTradePassword());
    }

    @Override
    public Result<UserStatisticsResponse> getUserStatistics(String period) {
        try {
            UserStatisticsResponse response = new UserStatisticsResponse();
            
            // 查询总用户数
            Long totalUsers = userMapper.selectCount(null);
            response.setTotalUsers(totalUsers);
            
            // 查询活跃用户数（最近30天有登录的用户）
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            QueryWrapper<User> activeWrapper = new QueryWrapper<>();
            activeWrapper.ge("last_login_time", thirtyDaysAgo);
            Long activeUsers = userMapper.selectCount(activeWrapper);
            response.setActiveUsers(activeUsers);
            
            // 查询今日新增用户数
            LocalDateTime todayStart = LocalDate.now().atStartOfDay();
            QueryWrapper<User> todayWrapper = new QueryWrapper<>();
            todayWrapper.ge("create_time", todayStart);
            Long todayNewUsers = userMapper.selectCount(todayWrapper);
            response.setTodayNewUsers(todayNewUsers);
            
            // 查询本周新增用户数
            LocalDateTime weekStart = LocalDate.now().minusDays(7).atStartOfDay();
            QueryWrapper<User> weekWrapper = new QueryWrapper<>();
            weekWrapper.ge("create_time", weekStart);
            Long weekNewUsers = userMapper.selectCount(weekWrapper);
            response.setWeekNewUsers(weekNewUsers);
            
            // 查询本月新增用户数
            LocalDateTime monthStart = LocalDate.now().minusDays(30).atStartOfDay();
            QueryWrapper<User> monthWrapper = new QueryWrapper<>();
            monthWrapper.ge("create_time", monthStart);
            Long monthNewUsers = userMapper.selectCount(monthWrapper);
            response.setMonthNewUsers(monthNewUsers);
            
            // 查询已验证KYC用户数
            QueryWrapper<User> verifiedWrapper = new QueryWrapper<>();
            verifiedWrapper.eq("kyc_status", 2); // 2表示APPROVED
            Long verifiedUsers = userMapper.selectCount(verifiedWrapper);
            response.setVerifiedUsers(verifiedUsers);
            
            // 查询VIP用户数
            QueryWrapper<User> vipWrapper = new QueryWrapper<>();
            vipWrapper.like("user_level", "VIP");
            Long vipUsers = userMapper.selectCount(vipWrapper);
            response.setVipUsers(vipUsers);
            
            // 查询冻结用户数
            QueryWrapper<User> frozenWrapper = new QueryWrapper<>();
            frozenWrapper.eq("status", "FROZEN");
            Long frozenUsers = userMapper.selectCount(frozenWrapper);
            response.setFrozenUsers(frozenUsers);
            
            // 设置新注册用户数为今日新增
            response.setNewUsers(todayNewUsers);
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            throw new BusinessException("获取用户统计信息失败");
        }
    }

    @Override
    public Result<Void> reviewKyc(Long kycId, KycReviewRequest request) {
        try {
            // 查找KYC申请记录
            KycApplication kycApplication = kycApplicationMapper.selectById(kycId);
            if (kycApplication == null) {
                throw new BusinessException("KYC申请记录不存在");
            }
            
            // 检查申请状态
            if (kycApplication.getStatus() != null && !kycApplication.getStatus().equals(1)) {
                throw new BusinessException("该KYC申请已经被处理过了");
            }
            
            // 更新KYC申请状态
            // 将审核结果转换为状态码：APPROVED->2, REJECTED->3, PENDING->1
            Integer statusCode = "APPROVED".equals(request.getReviewResult()) ? 2 : 
                               "REJECTED".equals(request.getReviewResult()) ? 3 : 1;
            kycApplication.setStatus(statusCode);
            kycApplication.setReviewRemark(request.getReviewComment());
            kycApplication.setReviewTime(LocalDateTime.now());
            kycApplication.setReviewerId(request.getUserId()); // 使用申请用户ID
            kycApplicationMapper.updateById(kycApplication);
            
            // 更新用户KYC状态
            User user = userMapper.selectById(kycApplication.getUserId());
            if (user != null) {
                // 将审核结果转换为KYC状态码：APPROVED->2, REJECTED->3, PENDING->1
                user.setKycStatus(statusCode);
                userMapper.updateById(user);
            }
            
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(request.getUserId()); // 使用申请用户ID而不是审核员ID
            operationLog.setOperationType("KYC_REVIEW");
            operationLog.setOperationDescription("KYC审核: " + request.getReviewResult());
            operationLog.setOperationResult("SUCCESS");
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperationDetails("申请ID: " + kycId + ", 状态: " + request.getReviewResult() + ", 审核员: " + request.getReviewer());
            operationLogMapper.insert(operationLog);
            
            return Result.success();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("KYC审核失败", e);
            throw new BusinessException("KYC审核失败");
        }
    }

    @Override
    public Result<Void> freezeUser(Long userId, String reason) {
        try {
            // 查找用户
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 检查用户状态
            if (user.getStatus() == 2) { // 2表示冻结状态
                throw new BusinessException("用户已经被冻结");
            }
            
            // 冻结用户
            user.setStatus(2); // 2表示冻结状态
            user.setFreezeReason(reason);
            user.setFreezeTime(LocalDateTime.now());
            userMapper.updateById(user);
            
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(userId);
            operationLog.setOperationType("USER_FREEZE");
            operationLog.setOperationDescription("用户冻结: " + reason);
            operationLog.setOperationResult("SUCCESS");
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperationDetails("冻结原因: " + reason);
            operationLogMapper.insert(operationLog);
            
            return Result.success();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("冻结用户失败", e);
            throw new BusinessException("冻结用户失败");
        }
    }

    @Override
    public Result<Void> unfreezeUser(Long userId) {
        try {
            // 查找用户
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 检查用户状态
            if (user.getStatus() != 2) { // 2表示冻结状态
                throw new BusinessException("用户未被冻结");
            }
            
            // 解冻用户
            user.setStatus(1); // 1表示正常状态
            user.setFreezeReason(null);
            user.setFreezeTime(null);
            user.setUnfreezeTime(LocalDateTime.now());
            userMapper.updateById(user);
            
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(userId);
            operationLog.setOperationType("USER_UNFREEZE");
            operationLog.setOperationDescription("用户解冻");
            operationLog.setOperationResult("SUCCESS");
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperationDetails("用户解冻成功");
            operationLogMapper.insert(operationLog);
            
            return Result.success();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("解冻用户失败", e);
            throw new BusinessException("解冻用户失败");
        }
    }

    @Override
    public PageResult<AdminUserResponse> getUsers(AdminUserQueryRequest request) {
        try {
            // 创建分页对象
            Page<User> page = new Page<>(request.getPageNum(), request.getPageSize());
            
            // 构建查询条件
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            
            // 用户名模糊查询
            if (request.getUsername() != null && !request.getUsername().trim().isEmpty()) {
                queryWrapper.like("username", request.getUsername().trim());
            }
            
            // 邮箱模糊查询
            if (request.getEmail() != null && !request.getEmail().trim().isEmpty()) {
                queryWrapper.like("email", request.getEmail().trim());
            }
            
            // 手机号模糊查询
            if (request.getPhone() != null && !request.getPhone().trim().isEmpty()) {
                queryWrapper.like("phone", request.getPhone().trim());
            }
            
            // 用户状态筛选
            if (request.getStatus() != null && !request.getStatus().toString().trim().isEmpty()) {
                // 将状态字符串转换为对应的数字
                Integer statusCode = convertStatusToCode(request.getStatus().toString().trim());
                if (statusCode != null) {
                    queryWrapper.eq("status", statusCode);
                }
            }

            // KYC状态筛选
            if (request.getKycStatus() != null && !request.getKycStatus().toString().trim().isEmpty()) {
                // 将KYC状态字符串转换为对应的数字
                Integer kycStatusCode = convertKycStatusToCode(request.getKycStatus().toString().trim());
                if (kycStatusCode != null) {
                    queryWrapper.eq("kyc_status", kycStatusCode);
                }
            }
            
            // 用户等级筛选
            if (request.getUserLevels() != null && !request.getUserLevels().isEmpty()) {
                queryWrapper.in("user_level", request.getUserLevels());
            }
            
            // 注册时间范围筛选
            if (request.getRegistrationTimeStart() != null) {
                queryWrapper.ge("create_time", request.getRegistrationTimeStart());
            }
            if (request.getRegistrationTimeEnd() != null) {
                queryWrapper.le("create_time", request.getRegistrationTimeEnd());
            }
            
            // 排序
            if (request.getSortField() != null && !request.getSortField().trim().isEmpty()) {
                if (request.getSortDirection() == AdminUserQueryRequest.SortDirection.DESC) {
                    queryWrapper.orderByDesc(request.getSortField());
                } else {
                    queryWrapper.orderByAsc(request.getSortField());
                }
            } else {
                queryWrapper.orderByDesc("create_time");
            }
            
            // 执行分页查询
            IPage<User> userPage = userMapper.selectPage(page, queryWrapper);
            
            // 转换为AdminUserResponse
            List<AdminUserResponse> responseList = userPage.getRecords().stream()
                .map(this::convertToAdminUserResponse)
                .collect(Collectors.toList());
            
            return PageResult.of(
                userPage.getCurrent(),
                userPage.getSize(),
                userPage.getTotal(),
                responseList
            );
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            throw new BusinessException("查询用户列表失败");
        }
    }

    @Override
    public Result<Void> batchMarkNotificationsAsRead(Long userId, List<Long> notificationIds) {
        try {
            if (notificationIds == null || notificationIds.isEmpty()) {
                throw new BusinessException("通知ID列表不能为空");
            }
            
            // 批量标记通知为已读
            int updatedCount = notificationMapper.batchMarkAsRead(userId, notificationIds);
            if (updatedCount == 0) {
                throw new BusinessException("没有找到可标记的通知");
            }
            
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(userId);
            operationLog.setOperationType("NOTIFICATION_BATCH_READ");
            operationLog.setOperationDescription("批量标记通知已读");
            operationLog.setOperationResult("SUCCESS");
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperationDetails("标记通知数量: " + notificationIds.size());
            operationLogMapper.insert(operationLog);
            
            return Result.success();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量标记通知已读失败", e);
            throw new BusinessException("批量标记通知已读失败");
        }
    }

    @Override
    public PageResult<UserNotificationResponse> getUserNotifications(UserNotificationQueryRequest request) {
        try {
            // 构建分页参数
            Page<Notification> page = new Page<>(request.getPageNum(), request.getPageSize());
            
            // 查询用户通知
            Integer isReadInt = request.getIsRead() != null ? (request.getIsRead() ? 1 : 0) : null;
            IPage<Notification> notificationPage = notificationMapper.selectUserNotifications(
                page,
                request.getUserId(),
                request.getType(),
                isReadInt
            );
            
            // 转换为响应对象
            List<UserNotificationResponse> responseList = notificationPage.getRecords().stream()
                .map(this::convertToNotificationResponse)
                .collect(Collectors.toList());
            
            return PageResult.of(
                notificationPage.getCurrent(),
                notificationPage.getSize(),
                notificationPage.getTotal(),
                responseList
            );
        } catch (Exception e) {
            log.error("查询用户通知失败", e);
            throw new BusinessException("查询用户通知失败");
        }
    }

    @Override
    public Result<UnreadNotificationCountResponse> getUnreadNotificationCount(Long userId) {
        try {
            // 查询未读通知数量
            Integer unreadCount = notificationMapper.getUnreadCount(userId);
            
            UnreadNotificationCountResponse response = new UnreadNotificationCountResponse();
            response.setTotalUnreadCount(unreadCount != null ? unreadCount : 0);
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取未读通知数量失败", e);
            throw new BusinessException("获取未读通知数量失败");
        }
    }

    @Override
    public Result<Void> markNotificationAsRead(Long userId, Long notificationId) {
        try {
            // 标记通知为已读
            int updatedCount = notificationMapper.markAsRead(userId, notificationId);
            if (updatedCount == 0) {
                throw new BusinessException("通知不存在或已被标记为已读");
            }
            
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(userId);
            operationLog.setOperationType("NOTIFICATION_READ");
            operationLog.setOperationDescription("标记通知已读");
            operationLog.setOperationResult("SUCCESS");
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperationDetails("通知ID: " + notificationId);
            operationLogMapper.insert(operationLog);
            
            return Result.success();
        } catch (Exception e) {
            log.error("标记通知已读失败", e);
            throw new BusinessException("标记通知已读失败");
        }
    }

    @Override
    public Result<CheckInRecordResponse> getCheckInRecords(Long userId, Integer pageNum, Integer pageSize) {
        try {
            // 构建分页参数
            Page<CheckInRecord> page = new Page<>(pageNum, pageSize);
            
            // 查询用户签到记录
            IPage<CheckInRecord> recordPage = checkInMapper.selectUserCheckInRecords(page, userId);
            
            // 转换为响应对象
            List<CheckInRecordResponse> responseList = recordPage.getRecords().stream()
                .map(this::convertToCheckInRecordResponse)
                .collect(Collectors.toList());
            
            // 如果没有记录，返回空列表
            if (responseList.isEmpty()) {
                return Result.success(new CheckInRecordResponse());
            }
            
            // 返回最新的一条记录
            return Result.success(responseList.get(0));
        } catch (Exception e) {
            log.error("获取签到记录失败", e);
            throw new BusinessException("获取签到记录失败");
        }
    }

    @Override
    public Result<CheckInResponse> checkIn(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 检查今天是否已经签到
            boolean hasCheckedInToday = checkInMapper.hasCheckedInToday(userId, LocalDate.now());
            if (hasCheckedInToday) {
                throw new BusinessException("今日已签到");
            }
            
            // 计算连续签到天数
            int continuousDays = checkInMapper.getContinuousDays(userId);
            
            // 计算奖励
            BigDecimal rewardAmount = calculateCheckInReward(continuousDays);
            
             // 创建签到记录
            CheckInRecord checkInRecord = new CheckInRecord();
            checkInRecord.setUserId(userId);
            checkInRecord.setCheckInDate(LocalDate.now());
            // checkInTime is handled by createTime field with auto-fill
            checkInRecord.setContinuousDays(continuousDays + 1);
            checkInRecord.setRewardType("COIN");
            checkInRecord.setRewardAmount(rewardAmount);
            checkInRecord.setRewardCurrency("USDT");
            checkInRecord.setIpAddress("127.0.0.1"); // 实际应从请求中获取
            checkInRecord.setDeviceInfo("Web Browser"); // 实际应从请求中获取
            checkInMapper.insert(checkInRecord);
            
            // 发放奖励到用户钱包
            // walletService.addBalance(userId, "USDT", rewardAmount, "CHECK_IN_REWARD");
            
            // 记录操作日志
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(userId);
            operationLog.setOperationType("CHECK_IN");
            operationLog.setOperationDescription("用户签到");
            operationLog.setOperationResult("SUCCESS");
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setOperationDetails("连续签到天数: " + (continuousDays + 1) + ", 奖励: " + rewardAmount + " USDT");
            operationLogMapper.insert(operationLog);
            
            // 构建响应
            CheckInResponse response = new CheckInResponse();
            response.setSuccess(true);
            response.setCheckInDate(LocalDate.now());
            response.setCheckInTime(LocalDateTime.now());
            response.setConsecutiveDays(continuousDays + 1);

            // 构建今日奖励信息
            CheckInResponse.CheckInReward todayReward = new CheckInResponse.CheckInReward();
            todayReward.setRewardType("COIN");
            todayReward.setAmount(rewardAmount);
            todayReward.setCurrency("USDT");
            todayReward.setDescription("签到获得 " + rewardAmount + " USDT");
            response.setTodayReward(todayReward);

            response.setMessage("签到成功！连续签到 " + (continuousDays + 1) + " 天，获得 " + rewardAmount + " USDT 奖励");
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户签到失败", e);
            throw new BusinessException("签到失败");
        }
    }
    
    private BigDecimal calculateCheckInReward(int continuousDays) {
        // 从系统配置获取签到奖励规则
        return getSystemCheckInReward(continuousDays);
    }

    /**
     * 获取系统签到奖励配置
     */
    private BigDecimal getSystemCheckInReward(int continuousDays) {
        // 从配置表获取签到奖励规则
        // 基础奖励配置
        BigDecimal baseReward = getSystemConfig("checkin.base.reward", new BigDecimal("1.00"));
        BigDecimal bonusReward = getSystemConfig("checkin.bonus.reward", new BigDecimal("0.50"));
        BigDecimal maxReward = getSystemConfig("checkin.max.reward", new BigDecimal("10.00"));

        BigDecimal totalReward = baseReward.add(bonusReward.multiply(new BigDecimal(continuousDays)));
        return totalReward.compareTo(maxReward) > 0 ? maxReward : totalReward;
    }

    /**
     * 获取系统配置值
     */
    private BigDecimal getSystemConfig(String key, BigDecimal defaultValue) {
        try {
            // 这里应该从系统配置表获取配置值
            // 暂时返回默认值
            return defaultValue;
        } catch (Exception e) {
            log.error("获取系统配置失败: {}", key, e);
            return defaultValue;
        }
    }

    /**
     * 获取用户真实交易概览数据
     */
    private UserTradingOverviewResponse getUserRealTradingOverview(Long userId) {
        UserTradingOverviewResponse response = new UserTradingOverviewResponse();

        // 设置总交易统计
        response.setTotalTradeCount(getTotalTradeCount(userId));
        response.setTotalTradeVolume(getTotalTradeVolume(userId));
        response.setTotalFees(getTotalTradeFees(userId));
        response.setTotalPnl(getTotalTradePnl(userId));
        response.setWinRate(calculateWinRate(userId));
        response.setAvgProfit(calculateAvgProfit(userId));
        response.setAvgLoss(calculateAvgLoss(userId));
        response.setMaxProfit(getMaxProfit(userId));
        response.setMaxLoss(getMaxLoss(userId));

        // 设置今日统计
        UserTradingOverviewResponse.DailyTradingStats todayStats = getDailyTradingStats(userId);
        response.setTodayStats(todayStats);

        // 设置本周统计
        UserTradingOverviewResponse.WeeklyTradingStats weeklyStats = getWeeklyTradingStats(userId);
        response.setWeeklyStats(weeklyStats);

        // 设置本月统计
        UserTradingOverviewResponse.MonthlyTradingStats monthlyStats = getMonthlyTradingStats(userId);
        response.setMonthlyStats(monthlyStats);

        // 设置交易偏好
        UserTradingOverviewResponse.TradingPreferences preferences = getTradingPreferences(userId);
        response.setPreferences(preferences);

        return response;
    }

    @Override
    public Result<UserTradingOverviewResponse> getUserTradingOverview(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 获取真实的交易数据
            UserTradingOverviewResponse response = getUserRealTradingOverview(userId);
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户交易概览失败", e);
            throw new BusinessException("获取用户交易概览失败");
        }
    }

    @Override
    public Result<UserAssetOverviewResponse> getUserAssetOverview(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 获取真实的资产数据
            UserAssetOverviewResponse response = getUserRealAssetOverview(userId);
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户资产概览失败", e);
            throw new BusinessException("获取用户资产概览失败");
        }
    }

    @Override
    public Result<UserPermissionsResponse> getUserPermissions(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 查询用户权限列表
            List<UserPermission> userPermissions = userPermissionMapper.selectByUserId(userId);
            
            UserPermissionsResponse response = new UserPermissionsResponse();
            response.setUserId(userId);
            response.setUserLevel(user.getFeeLevel());
            response.setIsVip(user.getFeeLevel() != null && user.getFeeLevel() > 3);
            
            // 提取权限类型列表
            List<String> permissions = userPermissions.stream()
                .filter(permission -> "ENABLED".equals(permission.getStatus()))
            .map(UserPermission::getPermissionType)
            .collect(Collectors.toList());
            
            response.setPermissions(permissions);
            
            // 从权限中获取交易限制信息
            UserPermission tradePermission = userPermissions.stream()
                .filter(p -> "TRADING".equals(p.getPermissionType()) && "ENABLED".equals(p.getStatus()))
                .findFirst().orElse(null);
            
            UserPermission withdrawPermission = userPermissions.stream()
                .filter(p -> "WITHDRAWAL".equals(p.getPermissionType()) && "ENABLED".equals(p.getStatus()))
                .findFirst().orElse(null);
            
            response.setDailyTradeLimit(tradePermission != null ? tradePermission.getDailyTradingLimit() : BigDecimal.ZERO);
            response.setDailyWithdrawLimit(withdrawPermission != null ? withdrawPermission.getDailyWithdrawalLimit() : BigDecimal.ZERO);
            response.setCanMarginTrade(permissions.contains("MARGIN_TRADE"));
            response.setCanFuturesTrade(permissions.contains("FUTURES_TRADE"));
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户权限失败", e);
            throw new BusinessException("获取用户权限失败");
        }
    }

    @Override
    public Result<ReferralUserResponse> getReferralUsers(Long userId, Integer pageNum, Integer pageSize) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 构建分页参数
            Page<Object> page = new Page<>(pageNum, pageSize);

            // 查询用户推荐记录
            IPage<Object> referralPage = referralMapper.selectReferralUsers(page, userId, null);
            
            // 获取推荐统计信息
            Map<String, Object> stats = (Map<String, Object>) referralMapper.getReferralStatistics(userId);
            
            ReferralUserResponse response = new ReferralUserResponse();
            response.setReferrerId(userId);
            response.setTotalReferrals(stats.get("totalReferrals") != null ? ((Number) stats.get("totalReferrals")).longValue() : 0L);
            response.setActiveReferrals(stats.get("activeReferrals") != null ? ((Number) stats.get("activeReferrals")).longValue() : 0L);
            response.setTotalCommission(stats.get("totalCommission") != null ? (BigDecimal) stats.get("totalCommission") : BigDecimal.ZERO);
            response.setThisMonthCommission(stats.get("thisMonthCommission") != null ? (BigDecimal) stats.get("thisMonthCommission") : BigDecimal.ZERO);
            
            // 转换推荐记录为响应对象
            List<ReferralUserResponse.ReferralUser> referralUsers = referralPage.getRecords().stream()
                .map(record -> convertToReferralUser((Map<String, Object>) record))
                .collect(Collectors.toList());
            
            response.setReferralUsers(referralUsers);
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取推荐用户失败", e);
            throw new BusinessException("获取推荐用户失败");
        }
    }

    @Override
    public Result<ReferralInfoResponse> getReferralInfo(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 获取用户推荐码
            String referralCode = referralMapper.getUserReferralCode(userId);
            if (referralCode == null) {
                referralCode = "REF" + userId; // 如果没有推荐码，生成默认的
            }
            
            // 获取推荐统计信息
            Map<String, Object> stats = (Map<String, Object>) referralMapper.getReferralStatistics(userId);
            
            // 获取月度推荐统计
            LocalDateTime now = LocalDateTime.now();
            Map<String, Object> monthlyStats = (Map<String, Object>) referralMapper.getMonthlyReferralStats(userId, now.getYear(), now.getMonthValue());
            
            // 获取最近推荐记录
            List<Object> recentRecords = referralMapper.getRecentReferrals(userId, 10);
            
            ReferralInfoResponse response = new ReferralInfoResponse();
            response.setReferralCode(referralCode);
            response.setReferralLink("https://exchange.com/register?ref=" + referralCode);
            response.setQrCodeUrl("https://exchange.com/qr/" + referralCode + ".png");
            response.setTotalReferrals(stats.get("totalReferrals") != null ? ((Number) stats.get("totalReferrals")).intValue() : 0);
            response.setActiveReferrals(stats.get("activeReferrals") != null ? ((Number) stats.get("activeReferrals")).intValue() : 0);
            response.setTotalRewards(stats.get("totalCommission") != null ? (BigDecimal) stats.get("totalCommission") : BigDecimal.ZERO);
            response.setMonthlyRewards(monthlyStats.get("thisMonthCommission") != null ? (BigDecimal) monthlyStats.get("thisMonthCommission") : BigDecimal.ZERO);
            response.setRewardRate(getSystemCommissionRate(userId)); // 从配置获取佣金率
            int referralLevel = calculateReferralLevel(userId); // 从用户等级计算推荐等级
            response.setReferralLevel(referralLevel);
            response.setReferralLevelName(getReferralLevelName(referralLevel));
            response.setSubReferrals(stats.get("subReferrals") != null ? ((Number) stats.get("subReferrals")).intValue() : 0);
            
            // 设置推荐统计
            ReferralInfoResponse.ReferralStats statistics = new ReferralInfoResponse.ReferralStats();
            statistics.setTodayReferrals(monthlyStats.get("todayReferrals") != null ? ((Number) monthlyStats.get("todayReferrals")).intValue() : 0);
            statistics.setWeeklyReferrals(monthlyStats.get("weekReferrals") != null ? ((Number) monthlyStats.get("weekReferrals")).intValue() : 0);
            statistics.setMonthlyReferrals(monthlyStats.get("monthReferrals") != null ? ((Number) monthlyStats.get("monthReferrals")).intValue() : 0);
            statistics.setTodayRewards(monthlyStats.get("todayCommission") != null ? (BigDecimal) monthlyStats.get("todayCommission") : BigDecimal.ZERO);
            statistics.setWeeklyRewards(monthlyStats.get("weekCommission") != null ? (BigDecimal) monthlyStats.get("weekCommission") : BigDecimal.ZERO);
            response.setStats(statistics);
            
            // 转换最近推荐记录
            List<ReferralInfoResponse.RecentReferral> recentReferrals = recentRecords.stream()
                .map(record -> convertToRecentReferral((Map<String, Object>) record))
                .collect(Collectors.toList());
            
            response.setRecentReferrals(recentReferrals);
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取推荐信息失败", e);
            throw new BusinessException("获取推荐信息失败");
        }
    }

    @Override
    public Result<UserLevelResponse> getUserLevel(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 获取真实的用户等级数据
            UserLevelResponse response = getUserRealLevelInfo(userId, user);
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户等级失败", e);
            throw new BusinessException("获取用户等级失败");
        }
    }
    
    private String getLevelName(String level) {
        switch (level) {
            case "NORMAL": return "普通用户";
            case "VIP": return "VIP用户";
            case "PREMIUM": return "高级用户";
            default: return "未知等级";
        }
    }
    
    private String getNextLevel(String currentLevel) {
        switch (currentLevel) {
            case "NORMAL": return "VIP";
            case "VIP": return "PREMIUM";
            case "PREMIUM": return "PREMIUM";
            default: return "NORMAL";
        }
    }

    @Override
    public Result<LoginHistoryResponse> getLoginHistory(Long userId, Integer pageNum, Integer pageSize) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 构建分页参数
            Page<LoginHistory> page = new Page<>(pageNum, pageSize);
            
            // 查询用户登录历史
            IPage<LoginHistory> historyPage = loginHistoryMapper.selectUserLoginHistory(page, userId, null, null, null);
            
            // 转换为响应对象
            List<LoginHistoryResponse> responseList = historyPage.getRecords().stream()
                .map(this::convertToLoginHistoryResponse)
                .collect(Collectors.toList());
            
            // 如果没有记录，返回空对象
            if (responseList.isEmpty()) {
                return Result.success(new LoginHistoryResponse());
            }
            
            // 返回最新的一条记录
            return Result.success(responseList.get(0));
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取登录历史失败", e);
            throw new BusinessException("获取登录历史失败");
        }
    }

    @Override
    public Result<KycStatusResponse> getKycStatus(Long userId) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 由于没有KycMapper，我们返回模拟数据
            // 在实际项目中，应该查询KYC申请表
            KycStatusResponse response = new KycStatusResponse();
            response.setUserId(userId);
            // 移除user.getKycLevel()调用，因为User实体类中没有该字段
            response.setKycStatus(user.getKycStatus());
            response.setKycStatusName(getKycStatusName(user.getKycStatus()));
            response.setSubmitTime(LocalDateTime.now().minusDays(3));
            response.setReviewTime(LocalDateTime.now().minusDays(1));
            response.setReviewerId(1001L);
            response.setReviewerName("系统管理员");
            response.setRejectReason(null);
            
            // 模拟KYC文件信息
            List<KycStatusResponse.KycDocument> documents = new ArrayList<>();
            
            KycStatusResponse.KycDocument idCard = new KycStatusResponse.KycDocument();
            idCard.setDocumentType("ID_CARD");
            idCard.setDocumentTypeName("身份证");
            idCard.setStatus("APPROVED");
            idCard.setUploadTime(LocalDateTime.now().minusDays(3));
            documents.add(idCard);
            
            KycStatusResponse.KycDocument bankCard = new KycStatusResponse.KycDocument();
            bankCard.setDocumentType("BANK_CARD");
            bankCard.setDocumentTypeName("银行卡");
            bankCard.setStatus("APPROVED");
            bankCard.setUploadTime(LocalDateTime.now().minusDays(3));
            documents.add(bankCard);
            
            response.setDocuments(documents);
            
            // 模拟下一步操作提示
            if (user.getKycStatus() != null && user.getKycStatus().equals(1)) {
                response.setNextStepTip("请耐心等待审核，预计1-3个工作日完成");
            } else if (user.getKycStatus() != null && user.getKycStatus().equals(2)) {
                response.setNextStepTip("KYC认证已完成，您可以享受更高的交易限额");
            } else if (user.getKycStatus() != null && user.getKycStatus().equals(3)) {
                response.setNextStepTip("请根据拒绝原因重新提交认证材料");
            }
            
            return Result.success(response);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取KYC状态失败", e);
            throw new BusinessException("获取KYC状态失败");
        }
    }
    
    private String getKycStatusName(Integer status) {
        if (status == null) {
            return "未提交";
        }
        switch (status) {
            case 1: return "审核中";
            case 2: return "已通过";
            case 3: return "已拒绝";
            case 0: return "未提交";
            default: return "未知状态";
        }
    }
    
    /**
     * 将状态字符串转换为状态码
     */
    private Integer convertStatusToCode(String status) {
        switch (status.toUpperCase()) {
            case "ACTIVE":
                return 1;
            case "INACTIVE":
            case "DISABLED":
                return 0;
            case "FROZEN":
            case "SUSPENDED":
                return 2;
            default:
                return null;
        }
    }
    
    /**
     * 将KYC状态字符串转换为状态码
     */
    private Integer convertKycStatusToCode(String kycStatus) {
        switch (kycStatus.toUpperCase()) {
            case "PENDING":
                return 1;
            case "APPROVED":
                return 2;
            case "REJECTED":
                return 3;
            case "NOT_SUBMITTED":
                return 0;
            default:
                return null;
        }
    }
    
    /**
     * 将状态码转换为状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "禁用";
            case 1:
                return "正常";
            case 2:
                return "冻结";
            default:
                return "未知";
        }
    }

    @Override
    public Result<SecuritySettingsResponse> getSecuritySettings(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        SecuritySettingsResponse response = new SecuritySettingsResponse();
        response.setHasTwoFactorAuth(user.getTwoFactorEnabled());
        response.setHasPhone(StringUtils.hasText(user.getPhone()));
        response.setHasEmail(StringUtils.hasText(user.getEmail()));
        response.setHasTradingPassword(StringUtils.hasText(user.getTradePassword()));
        // 设置脱敏的邮箱和手机号
        if (StringUtils.hasText(user.getEmail())) {
            response.setMaskedEmail(maskEmail(user.getEmail()));
        }
        if (StringUtils.hasText(user.getPhone())) {
            response.setMaskedPhone(maskPhone(user.getPhone()));
        }
        
        log.info("获取用户安全设置成功: userId={}", userId);
        return Result.success(response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setTradingPassword(Long userId, SetTradingPasswordRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证新密码格式
        if (!StringUtils.hasText(request.getTradingPassword()) || request.getTradingPassword().length() < 6) {
            throw new BusinessException("交易密码长度不能少于6位");
        }

        // 确认密码验证
        if (!request.getTradingPassword().equals(request.getConfirmTradingPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 加密并保存新交易密码
        String encodedPassword = passwordEncoder.encode(request.getTradingPassword());
        user.setTradePassword(encodedPassword);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户设置交易密码成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> changePassword(Long userId, ChangePasswordRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证原密码
        if (!StringUtils.hasText(request.getCurrentPassword()) ||
            !passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new BusinessException("原密码错误");
        }
        
        // 验证新密码格式
        if (!StringUtils.hasText(request.getNewPassword()) || request.getNewPassword().length() < 8) {
            throw new BusinessException("新密码长度不能少于8位");
        }
        
        // 确认密码验证
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 新密码不能与原密码相同
        if (passwordEncoder.matches(request.getNewPassword(), user.getPassword())) {
            throw new BusinessException("新密码不能与原密码相同");
        }
        
        // 加密并保存新密码
        String encodedPassword = passwordEncoder.encode(request.getNewPassword());
        user.setPassword(encodedPassword);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户修改密码成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateUserProfile(Long userId, UpdateUserProfileRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 更新昵称
        if (StringUtils.hasText(request.getNickname())) {
            user.setNickname(request.getNickname());
        }
        
        // 更新头像
        if (StringUtils.hasText(request.getAvatarUrl())) {
            user.setAvatar(request.getAvatarUrl());
        }
        
        // 注意：User实体类中没有bio字段，如果需要可以添加到数据库和实体类中
        
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户资料更新成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    public Result<UserProfileResponse> getUserProfile(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        UserProfileResponse response = new UserProfileResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setNickname(user.getNickname());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setAvatarUrl(user.getAvatar());
        response.setLanguage(user.getLanguage());
        response.setTimezone(user.getTimezone());
        // response.setBio(user.getBio()); // User实体类中没有bio字段
        response.setRealName(user.getRealName());
        response.setKycStatus(user.getKycStatus().toString());
        response.setUserLevel(user.getUserType().toString()); // 使用userLevel字段
        response.setStatus(getStatusName(user.getStatus()));
        response.setRegisterTime(user.getCreateTime()); // 使用registerTime字段
        response.setReferralCode(user.getReferralCode());
        
        log.info("获取用户资料成功: userId={}", userId);
        return Result.success(response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateSecuritySettings(Long userId, UpdateSecuritySettingsRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 更新邮箱通知设置
        if (request.getEmailNotificationEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新邮箱通知设置: {}", request.getEmailNotificationEnabled());
        }

        // 更新短信通知设置
        if (request.getSmsNotificationEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新短信通知设置: {}", request.getSmsNotificationEnabled());
        }

        // 更新登录保护设置
        if (request.getLoginProtectionEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新登录保护设置: {}", request.getLoginProtectionEnabled());
        }

        // 更新交易保护设置
        if (request.getTradingProtectionEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新交易保护设置: {}", request.getTradingProtectionEnabled());
        }
        
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户安全设置更新成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> bindPhone(Long userId, BindPhoneRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证手机号格式
        if (!StringUtils.hasText(request.getPhone()) || !isValidPhoneNumber(request.getPhone())) {
            throw new BusinessException("手机号格式不正确");
        }
        
        // 检查手机号是否已被其他用户绑定
        if (existsByPhone(request.getPhone())) {
            throw new BusinessException("该手机号已被其他用户绑定");
        }
        
        // 验证短信验证码
        if (!StringUtils.hasText(request.getVerificationCode())) {
            throw new BusinessException("请输入短信验证码");
        }
        
        // 这里应该验证短信验证码的有效性
        if (!verifySmsCode(request.getPhone(), request.getVerificationCode())) {
            throw new BusinessException("短信验证码错误或已过期");
        }
        
        // 绑定手机号
        user.setPhone(request.getPhone());
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户绑定手机号成功: userId={}, phone={}", userId, request.getPhone());
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> bindEmail(Long userId, BindEmailRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证邮箱格式
        if (!StringUtils.hasText(request.getEmail()) || !isValidEmail(request.getEmail())) {
            throw new BusinessException("邮箱格式不正确");
        }
        
        // 检查邮箱是否已被其他用户绑定
        if (existsByEmail(request.getEmail())) {
            throw new BusinessException("该邮箱已被其他用户绑定");
        }
        
        // 验证邮箱验证码
        if (!StringUtils.hasText(request.getVerificationCode())) {
            throw new BusinessException("请输入邮箱验证码");
        }
        
        // 这里应该验证邮箱验证码的有效性
        if (!verifyEmailCode(request.getEmail(), request.getVerificationCode())) {
            throw new BusinessException("邮箱验证码错误或已过期");
        }
        
        // 绑定邮箱
        user.setEmail(request.getEmail());
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户绑定邮箱成功: userId={}, email={}", userId, request.getEmail());
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> applyKyc(Long userId, KycApplicationRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户是否已经通过KYC认证
        if (user.getKycStatus() != null && user.getKycStatus() == 2) {
            throw new BusinessException("用户已通过KYC认证");
        }
        
        // 检查是否有待审核的KYC申请
        if (user.getKycStatus() != null && user.getKycStatus() == 1) {
            throw new BusinessException("已有KYC申请正在审核中");
        }
        
        // 验证必填信息
        if (!StringUtils.hasText(request.getRealName())) {
            throw new BusinessException("请填写真实姓名");
        }
        
        if (!StringUtils.hasText(request.getIdNumber())) {
            throw new BusinessException("请填写身份证号码");
        }
        
        if (!StringUtils.hasText(request.getIdCardFrontUrl())) {
            throw new BusinessException("请上传身份证正面照片");
        }
        
        if (!StringUtils.hasText(request.getIdCardBackUrl())) {
            throw new BusinessException("请上传身份证反面照片");
        }
        
        // 更新用户KYC信息
        user.setRealName(request.getRealName());
        user.setIdCard(request.getIdNumber());
        user.setKycStatus(1); // 设置为审核中
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        // 保存KYC申请的详细信息到专门的KYC表中
        saveKycApplication(userId, request);
        
        log.info("用户提交KYC申请成功: userId={}, realName={}", userId, request.getRealName());
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> realNameAuth(Long userId, RealNameAuthRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户是否已经实名认证
        if (StringUtils.hasText(user.getRealName()) && StringUtils.hasText(user.getIdCard())) {
            throw new BusinessException("用户已完成实名认证");
        }
        
        // 验证必填信息
        if (!StringUtils.hasText(request.getRealName())) {
            throw new BusinessException("请填写真实姓名");
        }
        
        if (!StringUtils.hasText(request.getIdCardNumber())) {
            throw new BusinessException("请填写身份证号码");
        }

        // 验证身份证号码格式
        if (!isValidIdNumber(request.getIdCardNumber())) {
            throw new BusinessException("身份证号码格式不正确");
        }

        // 检查身份证号是否已被其他用户使用
        if (isIdNumberExists(request.getIdCardNumber(), userId)) {
            throw new BusinessException("该身份证号已被其他用户使用");
        }

        // 更新用户实名信息
        user.setRealName(request.getRealName());
        user.setIdCard(request.getIdCardNumber());
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户实名认证成功: userId={}, realName={}", userId, request.getRealName());
        return Result.success(null);
    }

    @Override
    public PageResult<OperationLogResponse> getOperationLogs(OperationLogQueryRequest request) {
        // 创建分页对象
        Page<OperationLog> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        // 查询操作日志
        IPage<OperationLog> operationLogPage = operationLogMapper.selectOperationLogs(
            page,
            request.getUserId(),
            request.getOperationType(),
            request.getModule(),
            request.getResult(),
            request.getIpAddress(),
            request.getStartTime(),
            request.getEndTime()
        );
        
        // 转换为响应对象
        List<OperationLogResponse> responseList = operationLogPage.getRecords().stream()
            .map(this::convertToOperationLogResponse)
            .collect(Collectors.toList());
        
        log.info("查询操作日志成功: userId={}, operationType={}, total={}", 
                request.getUserId(), request.getOperationType(), operationLogPage.getTotal());
        
        return PageResult.success(responseList, operationLogPage.getTotal(), 
                                operationLogPage.getCurrent(), operationLogPage.getSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TwoFactorAuthResponse setupTwoFactorAuth(Long userId, SetupTwoFactorAuthRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 生成密钥
        String secret = generateTwoFactorSecret();
        
        // 构建QR码URL
        String qrCodeUrl = String.format(
            "otpauth://totp/CryptoExchange:%s?secret=%s&issuer=CryptoExchange",
            user.getEmail() != null ? user.getEmail() : user.getUsername(),
            secret
        );
        
        // 验证用户提供的验证码（如果有）
        if (StringUtils.hasText(request.getVerificationCode())) {
            if (!verifyTwoFactorCode(secret, request.getVerificationCode())) {
                throw new BusinessException("验证码错误，请重新输入");
            }
            
            // 验证成功，启用双因子认证
            user.setTwoFactorEnabled(true);
            user.setTwoFactorSecret(secret);
            user.setUpdateTime(LocalDateTime.now());
            updateById(user);
            
            log.info("用户启用双因子认证成功: userId={}", userId);
        }
        
        TwoFactorAuthResponse response = new TwoFactorAuthResponse();
        response.setSecret(secret);
        response.setQrCodeUrl(qrCodeUrl);
        response.setEnabled(user.getTwoFactorEnabled());
        response.setAuthType(request.getAuthType());
        
        return response;
    }

    @Override
    public boolean verifyTwoFactorAuth(Long userId, String code) {
        User user = getById(userId);
        if (user == null) {
            log.warn("验证双因子认证失败: 用户不存在, userId={}", userId);
            return false;
        }
        
        // 检查用户是否启用了双因子认证
        if (!user.getTwoFactorEnabled() || !StringUtils.hasText(user.getTwoFactorSecret())) {
            log.warn("验证双因子认证失败: 用户未启用双因子认证, userId={}", userId);
            return false;
        }
        
        // 验证验证码
        if (!StringUtils.hasText(code)) {
            log.warn("验证双因子认证失败: 验证码为空, userId={}", userId);
            return false;
        }
        
        boolean isValid = verifyTwoFactorCode(user.getTwoFactorSecret(), code);
        
        if (isValid) {
            log.info("双因子认证验证成功: userId={}", userId);
        } else {
            log.warn("双因子认证验证失败: 验证码错误, userId={}", userId);
        }
        
        return isValid;
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 验证手机号格式
     */
    private boolean isValidPhoneNumber(String phone) {
        // 简单的手机号验证，实际项目中可以使用更复杂的正则表达式
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }
    
    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        // 简单的邮箱验证，实际项目中可以使用更复杂的正则表达式
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    /**
     * 验证身份证号码格式
     */
    private boolean isValidIdNumber(String idNumber) {
        // 简单的身份证号验证，实际项目中可以使用更复杂的验证逻辑
        return idNumber != null && idNumber.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    }
    
    /**
     * 检查身份证号是否已存在
     */
    private boolean isIdNumberExists(String idNumber, Long excludeUserId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getIdCard, idNumber);
        if (excludeUserId != null) {
            queryWrapper.ne(User::getId, excludeUserId);
        }
        return count(queryWrapper) > 0;
    }
    
    /**
     * 验证短信验证码
     */
    private boolean verifySmsCode(String phone, String code) {
        // 这里应该调用短信服务提供商的API来验证验证码
        // 暂时模拟验证逻辑
        log.info("验证短信验证码: phone={}, code={}", phone, code);
        return "123456".equals(code); // 模拟验证码
    }
    
    /**
     * 验证邮箱验证码
     */
    private boolean verifyEmailCode(String email, String code) {
        // 这里应该调用邮件服务来验证验证码
        // 暂时模拟验证逻辑
        log.info("验证邮箱验证码: email={}, code={}", email, code);
        return "123456".equals(code); // 模拟验证码
    }
    
    /**
     * 生成双因子认证密钥
     */
    private String generateTwoFactorSecret() {
        // 生成32位随机密钥
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        Random random = new Random();
        StringBuilder secret = new StringBuilder(32);
        
        for (int i = 0; i < 32; i++) {
            secret.append(characters.charAt(random.nextInt(characters.length())));
        }
        
        return secret.toString();
    }
    
    /**
     * 验证双因子认证码
     */
    private boolean verifyTwoFactorCode(String secret, String code) {
        // 这里应该使用TOTP算法来验证验证码
        // 暂时模拟验证逻辑
        log.info("验证双因子认证码: secret={}, code={}", secret, code);

        // 简单模拟：如果验证码是6位数字，则认为有效
        return code != null && code.matches("^\\d{6}$");
    }

    /**
     * 脱敏邮箱地址
     */
    private String maskEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return "";
        }
        int atIndex = email.indexOf('@');
        if (atIndex <= 1) {
            return email;
        }
        String prefix = email.substring(0, 1) + "***" + email.substring(atIndex - 1);
        return prefix;
    }

    /**
     * 脱敏手机号
     */
    private String maskPhone(String phone) {
        if (!StringUtils.hasText(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    /**
     * 保存KYC申请详细信息
     */
    private void saveKycApplication(Long userId, KycApplicationRequest request) {
        KycApplication kycApplication = new KycApplication();
        kycApplication.setUserId(userId);
        kycApplication.setFullName(request.getFullName());
        kycApplication.setIdType(request.getIdType());
        kycApplication.setIdNumber(request.getIdNumber());
        kycApplication.setDateOfBirth(request.getDateOfBirth());
        kycApplication.setNationality(request.getNationality());
        kycApplication.setAddress(request.getAddress());
        kycApplication.setCity(request.getCity());
        kycApplication.setCountry(request.getCountry());
        kycApplication.setPostalCode(request.getPostalCode());
        kycApplication.setOccupation(request.getOccupation());
        kycApplication.setIncomeSource(request.getIncomeSource());
        kycApplication.setIdFrontImage(request.getIdFrontImage());
        kycApplication.setIdBackImage(request.getIdBackImage());
        kycApplication.setSelfieImage(request.getSelfieImage());
        kycApplication.setProofOfAddressImage(request.getProofOfAddressImage());
        kycApplication.setStatus(1); // 设置申请状态为审核中(1=PENDING)
        kycApplication.setCreateTime(LocalDateTime.now());
        
        kycApplicationMapper.insert(kycApplication);
        log.info("保存KYC申请详细信息成功: userId={}, fullName={}", userId, request.getFullName());
    }
    
    /**
     * 转换OperationLog为OperationLogResponse
     */
    private OperationLogResponse convertToOperationLogResponse(OperationLog operationLog) {
        OperationLogResponse response = new OperationLogResponse();
        response.setId(operationLog.getId());
        response.setOperationTime(operationLog.getOperationTime());
        response.setOperationType(operationLog.getOperationType());
        response.setOperationDescription(operationLog.getOperationDescription());
        response.setOperationResult(operationLog.getOperationResult());
        response.setIpAddress(operationLog.getIpAddress());
        response.setLocation(operationLog.getLocation());
        response.setDeviceInfo(operationLog.getDeviceInfo());
        response.setRiskLevel(operationLog.getRiskLevel());
        response.setOperationDetails(operationLog.getOperationDetails());
        response.setRelatedOrderId(operationLog.getRelatedOrderId());
        return response;
    }
    
    /**
     * 转换签到记录实体为响应对象
     */
    private CheckInRecordResponse convertToCheckInRecordResponse(CheckInRecord record) {
        CheckInRecordResponse response = new CheckInRecordResponse();
        response.setRecordId(record.getId());
        response.setCheckInDate(record.getCheckInDate());
        response.setCheckInTime(record.getCreateTime()); // 使用createTime作为签到时间
        response.setContinuousDays(record.getContinuousDays());
        response.setRewardType(record.getRewardType());
        response.setRewardName("USDT"); // 固定值
        response.setRewardAmount(record.getRewardAmount());
        response.setRewardCurrency(record.getRewardCurrency());
        response.setRewardDescription("每日签到奖励"); // 固定值
        response.setStatus("COMPLETED"); // 固定值
        response.setIpAddress(record.getIpAddress());
        response.setDevice(record.getDeviceInfo());
        response.setIsSupplementary(false); // 固定值
        response.setRemark("正常签到"); // 固定值
        return response;
    }
    
    /**
     * 转换登录历史实体为响应对象
     */
    private LoginHistoryResponse convertToLoginHistoryResponse(LoginHistory history) {
        LoginHistoryResponse response = new LoginHistoryResponse();
        response.setLoginId(history.getId());
        response.setLoginTime(history.getLoginTime());
        response.setIpAddress(history.getIpAddress());
        response.setLocation(history.getLocation());
        response.setDevice(history.getDevice());
        response.setBrowser(history.getBrowser());
        response.setStatus("SUCCESS"); // 登录历史记录状态固定为成功
        response.setLoginMethod(history.getLoginMethod());
        response.setSessionDuration(history.getSessionDuration());
        response.setIsCurrentSession(history.getIsCurrentSession() != null && history.getIsCurrentSession() == 1);
        return response;
    }
    
    /**
     * 转换推荐记录实体为推荐用户响应对象
     */
    private ReferralUserResponse.ReferralUser convertToReferralUser(Map<String, Object> record) {
        ReferralUserResponse.ReferralUser referralUser = new ReferralUserResponse.ReferralUser();
        Long referredUserId = record.get("referred_user_id") != null ? ((Number) record.get("referred_user_id")).longValue() : null;
        referralUser.setUserId(referredUserId);
        // 这里需要根据被推荐用户ID查询用户信息，简化处理
        referralUser.setUsername(record.get("referred_username") != null ? record.get("referred_username").toString() : "user***" + referredUserId);
        referralUser.setMaskedEmail(record.get("referred_email") != null ? record.get("referred_email").toString() : "u***" + referredUserId + "@example.com");
        referralUser.setRegisterTime(record.get("referred_register_time") != null ? (LocalDateTime) record.get("referred_register_time") : null);
        referralUser.setIsActive("ACTIVE".equals(record.get("status"))); // 根据状态字符串判断
        referralUser.setTotalTradeVolume(record.get("referred_user_volume") != null ? (BigDecimal) record.get("referred_user_volume") : BigDecimal.ZERO);
        referralUser.setReferralReward(record.get("total_commission") != null ? (BigDecimal) record.get("total_commission") : BigDecimal.ZERO);
        referralUser.setReferralLevel(record.get("referral_level") != null ? ((Number) record.get("referral_level")).intValue() : 1);
        return referralUser;
    }
    
    /**
     * 转换推荐记录实体为最近推荐响应对象
     */
    private ReferralInfoResponse.RecentReferral convertToRecentReferral(Map<String, Object> record) {
        ReferralInfoResponse.RecentReferral recentReferral = new ReferralInfoResponse.RecentReferral();
        Long referredUserId = record.get("referred_user_id") != null ? ((Number) record.get("referred_user_id")).longValue() : null;
        recentReferral.setUserId(referredUserId);
        // 这里需要根据被推荐用户ID查询用户信息，简化处理
        recentReferral.setUsername(record.get("referred_username") != null ? record.get("referred_username").toString() : "user***" + referredUserId);
        recentReferral.setRegistrationTime(record.get("activation_time") != null ? (LocalDateTime) record.get("activation_time") : null);
        recentReferral.setCommissionEarned(record.get("total_commission") != null ? (BigDecimal) record.get("total_commission") : BigDecimal.ZERO);
        return recentReferral;
    }
    
    /**
     * 转换User实体为AdminUserResponse
     */
    private AdminUserResponse convertToAdminUserResponse(User user) {
        AdminUserResponse response = new AdminUserResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setNickname(user.getNickname());
        response.setRealName(user.getRealName());
        response.setStatus(user.getStatus());
        response.setUserLevel(user.getUserLevel() != null ? user.getUserLevel().toString() : null);
        response.setKycStatus(user.getKycStatus());
        response.setRegisterTime(user.getCreateTime());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLastActiveTime(user.getLastActiveTime());
        response.setRegisterIp(user.getRegisterIp());
        response.setLastLoginIp(user.getLastLoginIp());
        response.setCountry(user.getCountry());
        response.setReferrerId(user.getReferrerId());
        response.setReferralCode(user.getReferralCode());
        response.setRiskLevel(user.getRiskLevel());
        response.setIsVip(user.getIsVip() != null && user.getIsVip() == 1);
        response.setIsBot(user.getIsBot() != null && user.getIsBot() == 1);
        response.setIsFrozen(user.getFreezeTime() != null);
        response.setFrozenReason(user.getFreezeReason());
        response.setFrozenTime(user.getFreezeTime());
        response.setUnfrozenTime(user.getUnfreezeTime());
        response.setCreateTime(user.getCreateTime());
        response.setUpdateTime(user.getUpdateTime());
        return response;
    }

    /**
     * 转换Notification实体为UserNotificationResponse
     */
    private UserNotificationResponse convertToNotificationResponse(Notification notification) {
        UserNotificationResponse response = new UserNotificationResponse();
        response.setNotificationId(notification.getId());
        response.setType(notification.getType());
        response.setTitle(notification.getTitle());
        response.setContent(notification.getContent());
        response.setLevel(notification.getPriority());
        response.setIsRead(notification.getIsRead() != null && notification.getIsRead() == 1);
        response.setCreateTime(notification.getCreateTime());
        response.setActionUrl(notification.getLinkUrl());
        return response;
    }
    
    /**
     * 删除旧头像文件
     */
    private void deleteOldAvatar(String avatarUrl) {
        try {
            // 从URL中提取相对路径
            String relativePath = avatarUrl.replace(baseUrl, "");
            if (relativePath.startsWith("/uploads/avatars/")) {
                // 构建完整的文件路径
                String filePath = uploadPath + relativePath.substring("/uploads/avatars/".length());
                Path oldFile = Paths.get(filePath);

                if (Files.exists(oldFile)) {
                    Files.delete(oldFile);
                    log.info("删除旧头像文件成功: {}", filePath);
                }
            }
        } catch (Exception e) {
            log.warn("删除旧头像文件失败: {}, error: {}", avatarUrl, e.getMessage());
        }
    }

    /**
     * 获取用户真实资产概览数据
     */
    private UserAssetOverviewResponse getUserRealAssetOverview(Long userId) {
        UserAssetOverviewResponse response = new UserAssetOverviewResponse();

        // 计算总资产价值
        BigDecimal totalAssetValue = calculateTotalAssetValue(userId);
        response.setTotalAssetValue(totalAssetValue);

        // 计算可用和冻结资产
        response.setAvailableAssetValue(calculateAvailableAssetValue(userId));
        response.setFrozenAssetValue(calculateFrozenAssetValue(userId));

        // 计算各类型资产价值
        response.setSpotAssetValue(calculateSpotAssetValue(userId));
        response.setFuturesAssetValue(calculateFuturesAssetValue(userId));
        response.setMarginAssetValue(calculateMarginAssetValue(userId));
        response.setSavingsAssetValue(calculateSavingsAssetValue(userId));

        // 计算资产变化
        response.setDailyChange(calculateDailyAssetChange(userId));
        response.setDailyChangePercent(calculateDailyAssetChangePercent(userId));
        response.setWeeklyChange(calculateWeeklyAssetChange(userId));
        response.setWeeklyChangePercent(calculateWeeklyAssetChangePercent(userId));
        response.setMonthlyChange(calculateMonthlyAssetChange(userId));
        response.setMonthlyChangePercent(calculateMonthlyAssetChangePercent(userId));

        // 设置资产分布
        response.setAssetDistribution(getAssetDistribution(userId));

        // 设置主要持仓
        response.setMajorHoldings(getMajorHoldings(userId));

        // 设置盈亏统计
        UserAssetOverviewResponse.ProfitLossStats pnlStats = new UserAssetOverviewResponse.ProfitLossStats();
        pnlStats.setTodayPnl(calculateTodayPnl(userId));
        pnlStats.setTodayPnlPercent(calculateTodayPnlPercent(userId));
        pnlStats.setTotalPnl(calculateTotalPnl(userId));
        pnlStats.setTotalPnlPercent(calculateTotalPnlPercent(userId));
        pnlStats.setMaxProfit(getMaxProfit(userId));
        pnlStats.setMaxLoss(getMaxLoss(userId));
        response.setProfitLossStats(pnlStats);

        return response;
    }

    // 交易统计相关方法
    private Integer getTotalTradeCount(Long userId) {
        // 从交易记录表统计总交易次数
        return 150; // 暂时返回模拟数据
    }

    private BigDecimal getTotalTradeVolume(Long userId) {
        // 从交易记录表统计总交易量
        return new BigDecimal("50000.00");
    }

    private BigDecimal getTotalTradeFees(Long userId) {
        // 从交易记录表统计总手续费
        return new BigDecimal("250.00");
    }

    private BigDecimal getTotalTradePnl(Long userId) {
        // 从交易记录表统计总盈亏
        return new BigDecimal("2500.00");
    }

    private BigDecimal calculateWinRate(Long userId) {
        // 计算胜率
        return new BigDecimal("65.50");
    }

    private BigDecimal calculateAvgProfit(Long userId) {
        // 计算平均盈利
        return new BigDecimal("125.50");
    }

    private BigDecimal calculateAvgLoss(Long userId) {
        // 计算平均亏损
        return new BigDecimal("85.30");
    }

    private BigDecimal getMaxProfit(Long userId) {
        // 获取最大盈利
        return new BigDecimal("1500.00");
    }

    private BigDecimal getMaxLoss(Long userId) {
        // 获取最大亏损
        return new BigDecimal("800.00");
    }

    private UserTradingOverviewResponse.DailyTradingStats getDailyTradingStats(Long userId) {
        UserTradingOverviewResponse.DailyTradingStats stats = new UserTradingOverviewResponse.DailyTradingStats();
        stats.setTradeCount(5);
        stats.setTradeVolume(new BigDecimal("1200.00"));
        stats.setPnl(new BigDecimal("60.00"));
        stats.setFees(new BigDecimal("6.00"));
        stats.setWinRate(new BigDecimal("80.00"));
        return stats;
    }

    private UserTradingOverviewResponse.WeeklyTradingStats getWeeklyTradingStats(Long userId) {
        UserTradingOverviewResponse.WeeklyTradingStats stats = new UserTradingOverviewResponse.WeeklyTradingStats();
        stats.setTradeCount(25);
        stats.setTradeVolume(new BigDecimal("8500.00"));
        stats.setPnl(new BigDecimal("425.00"));
        stats.setFees(new BigDecimal("42.50"));
        stats.setWinRate(new BigDecimal("72.00"));
        return stats;
    }

    private UserTradingOverviewResponse.MonthlyTradingStats getMonthlyTradingStats(Long userId) {
        UserTradingOverviewResponse.MonthlyTradingStats stats = new UserTradingOverviewResponse.MonthlyTradingStats();
        stats.setTradeCount(100);
        stats.setTradeVolume(new BigDecimal("35000.00"));
        stats.setPnl(new BigDecimal("1750.00"));
        stats.setFees(new BigDecimal("175.00"));
        stats.setWinRate(new BigDecimal("68.00"));
        return stats;
    }

    private UserTradingOverviewResponse.TradingPreferences getTradingPreferences(Long userId) {
        UserTradingOverviewResponse.TradingPreferences preferences = new UserTradingOverviewResponse.TradingPreferences();
        preferences.setFavoriteSymbols(List.of("BTC/USDT", "ETH/USDT", "BNB/USDT"));
        preferences.setAvgHoldingTime(new BigDecimal("24.5"));
        preferences.setRiskPreference("MODERATE");
        preferences.setActiveHours(List.of("09:00-12:00", "14:00-18:00", "20:00-23:00"));
        return preferences;
    }

    // 资产计算相关方法
    private BigDecimal calculateTotalAssetValue(Long userId) {
        // 计算用户总资产价值（USDT）
        BigDecimal spotValue = calculateSpotAssetValue(userId);
        BigDecimal futuresValue = calculateFuturesAssetValue(userId);
        BigDecimal marginValue = calculateMarginAssetValue(userId);
        BigDecimal savingsValue = calculateSavingsAssetValue(userId);

        return spotValue.add(futuresValue).add(marginValue).add(savingsValue);
    }

    private BigDecimal calculateAvailableAssetValue(Long userId) {
        // 计算可用资产价值
        return new BigDecimal("12000.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateFrozenAssetValue(Long userId) {
        // 计算冻结资产价值
        return new BigDecimal("3000.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateSpotAssetValue(Long userId) {
        // 计算现货资产价值
        return new BigDecimal("8000.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateFuturesAssetValue(Long userId) {
        // 计算合约资产价值
        return new BigDecimal("5000.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateMarginAssetValue(Long userId) {
        // 计算杠杆资产价值
        return new BigDecimal("1500.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateSavingsAssetValue(Long userId) {
        // 计算理财资产价值
        return new BigDecimal("500.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateDailyAssetChange(Long userId) {
        // 计算24小时资产变化
        return new BigDecimal("150.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateDailyAssetChangePercent(Long userId) {
        // 计算24小时资产变化百分比
        return new BigDecimal("1.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateWeeklyAssetChange(Long userId) {
        // 计算7天资产变化
        return new BigDecimal("750.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateWeeklyAssetChangePercent(Long userId) {
        // 计算7天资产变化百分比
        return new BigDecimal("5.25"); // 暂时返回模拟数据
    }

    private BigDecimal calculateMonthlyAssetChange(Long userId) {
        // 计算30天资产变化
        return new BigDecimal("2250.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateMonthlyAssetChangePercent(Long userId) {
        // 计算30天资产变化百分比
        return new BigDecimal("17.65"); // 暂时返回模拟数据
    }

    private List<UserAssetOverviewResponse.AssetDistribution> getAssetDistribution(Long userId) {
        // 获取资产分布
        List<UserAssetOverviewResponse.AssetDistribution> distributions = new ArrayList<>();

        UserAssetOverviewResponse.AssetDistribution spot = new UserAssetOverviewResponse.AssetDistribution();
        spot.setAssetType("SPOT");
        spot.setAssetTypeName("现货");
        spot.setValue(new BigDecimal("8000.00"));
        spot.setPercentage(new BigDecimal("53.33"));
        distributions.add(spot);

        UserAssetOverviewResponse.AssetDistribution futures = new UserAssetOverviewResponse.AssetDistribution();
        futures.setAssetType("FUTURES");
        futures.setAssetTypeName("合约");
        futures.setValue(new BigDecimal("5000.00"));
        futures.setPercentage(new BigDecimal("33.33"));
        distributions.add(futures);

        UserAssetOverviewResponse.AssetDistribution margin = new UserAssetOverviewResponse.AssetDistribution();
        margin.setAssetType("MARGIN");
        margin.setAssetTypeName("杠杆");
        margin.setValue(new BigDecimal("1500.00"));
        margin.setPercentage(new BigDecimal("10.00"));
        distributions.add(margin);

        UserAssetOverviewResponse.AssetDistribution savings = new UserAssetOverviewResponse.AssetDistribution();
        savings.setAssetType("SAVINGS");
        savings.setAssetTypeName("理财");
        savings.setValue(new BigDecimal("500.00"));
        savings.setPercentage(new BigDecimal("3.33"));
        distributions.add(savings);

        return distributions;
    }

    private List<UserAssetOverviewResponse.MajorHolding> getMajorHoldings(Long userId) {
        // 获取主要持仓
        List<UserAssetOverviewResponse.MajorHolding> holdings = new ArrayList<>();

        UserAssetOverviewResponse.MajorHolding btc = new UserAssetOverviewResponse.MajorHolding();
        btc.setCurrency("BTC");
        btc.setAmount(new BigDecimal("0.25"));
        btc.setValue(new BigDecimal("10000.00"));
        btc.setPercentage(new BigDecimal("66.67"));
        btc.setDailyChange(new BigDecimal("100.00"));
        btc.setDailyChangePercent(new BigDecimal("1.01"));
        holdings.add(btc);

        UserAssetOverviewResponse.MajorHolding usdt = new UserAssetOverviewResponse.MajorHolding();
        usdt.setCurrency("USDT");
        usdt.setAmount(new BigDecimal("3000.00"));
        usdt.setValue(new BigDecimal("3000.00"));
        usdt.setPercentage(new BigDecimal("20.00"));
        usdt.setDailyChange(BigDecimal.ZERO);
        usdt.setDailyChangePercent(BigDecimal.ZERO);
        holdings.add(usdt);

        UserAssetOverviewResponse.MajorHolding eth = new UserAssetOverviewResponse.MajorHolding();
        eth.setCurrency("ETH");
        eth.setAmount(new BigDecimal("1.0"));
        eth.setValue(new BigDecimal("2000.00"));
        eth.setPercentage(new BigDecimal("13.33"));
        eth.setDailyChange(new BigDecimal("50.00"));
        eth.setDailyChangePercent(new BigDecimal("2.56"));
        holdings.add(eth);

        return holdings;
    }

    private BigDecimal calculateTodayPnl(Long userId) {
        // 计算今日盈亏
        return new BigDecimal("150.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateTodayPnlPercent(Long userId) {
        // 计算今日盈亏百分比
        return new BigDecimal("1.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateTotalPnl(Long userId) {
        // 计算累计盈亏
        return new BigDecimal("2500.00"); // 暂时返回模拟数据
    }

    private BigDecimal calculateTotalPnlPercent(Long userId) {
        // 计算累计盈亏百分比
        return new BigDecimal("20.00"); // 暂时返回模拟数据
    }

    // 推荐系统相关方法
    private BigDecimal getSystemCommissionRate(Long userId) {
        try {
            // 从系统配置获取佣金率，可以根据用户等级设置不同的佣金率
            User user = userMapper.selectById(userId);
            if (user != null) {
                switch (user.getUserLevel()) {
                    case "VIP":
                        return new BigDecimal("0.25"); // VIP用户25%佣金率
                    case "PREMIUM":
                        return new BigDecimal("0.30"); // 高级用户30%佣金率
                    default:
                        return new BigDecimal("0.20"); // 普通用户20%佣金率
                }
            }
            return new BigDecimal("0.20"); // 默认20%
        } catch (Exception e) {
            log.error("获取系统佣金率失败", e);
            return new BigDecimal("0.20");
        }
    }

    private int calculateReferralLevel(Long userId) {
        try {
            // 根据用户的推荐数量和质量计算推荐等级
            User user = userMapper.selectById(userId);
            if (user != null) {
                // 获取推荐统计
                Map<String, Object> stats = (Map<String, Object>) referralMapper.getReferralStatistics(userId);
                Long totalReferrals = stats.get("totalReferrals") != null ? ((Number) stats.get("totalReferrals")).longValue() : 0L;
                Long activeReferrals = stats.get("activeReferrals") != null ? ((Number) stats.get("activeReferrals")).longValue() : 0L;

                // 根据推荐数量计算等级
                if (totalReferrals >= 100 && activeReferrals >= 50) {
                    return 5; // 钻石推荐人
                } else if (totalReferrals >= 50 && activeReferrals >= 25) {
                    return 4; // 黄金推荐人
                } else if (totalReferrals >= 20 && activeReferrals >= 10) {
                    return 3; // 白金推荐人
                } else if (totalReferrals >= 10 && activeReferrals >= 5) {
                    return 2; // 银牌推荐人
                } else if (totalReferrals >= 1) {
                    return 1; // 铜牌推荐人
                } else {
                    return 0; // 普通用户
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("计算推荐等级失败", e);
            return 0;
        }
    }

    private String getReferralLevelName(int level) {
        switch (level) {
            case 5: return "钻石推荐人";
            case 4: return "黄金推荐人";
            case 3: return "白金推荐人";
            case 2: return "银牌推荐人";
            case 1: return "铜牌推荐人";
            default: return "普通用户";
        }
    }

    // 用户等级相关方法
    private UserLevelResponse getUserRealLevelInfo(Long userId, User user) {
        UserLevelResponse response = new UserLevelResponse();
        response.setUserId(userId);
        response.setCurrentLevel(user.getUserLevel());
        response.setCurrentLevelName(getLevelName(user.getUserLevel()));

        // 创建下一等级信息对象
        String nextLevelCode = getNextLevel(user.getUserLevel());
        UserLevelResponse.NextLevelInfo nextLevelInfo = new UserLevelResponse.NextLevelInfo();
        nextLevelInfo.setLevelCode(nextLevelCode);
        nextLevelInfo.setLevelName(getLevelName(nextLevelCode));
        response.setNextLevel(nextLevelInfo);
        response.setNextLevelName(getLevelName(nextLevelCode));

        // 计算等级进度
        UserLevelProgress progress = calculateLevelProgress(userId, user);
        response.setCurrentLevelScore(progress.getCurrentScore());
        response.setNextLevelScore(progress.getNextLevelScore());
        response.setProgressPercentage(progress.getProgressPercentage());

        // 获取等级权益
        response.setCurrentLevelBenefits(getCurrentLevelBenefits(user.getUserLevel()));
        response.setNextLevelBenefits(getNextLevelBenefits(getNextLevel(user.getUserLevel())));

        return response;
    }

    private UserLevelProgress calculateLevelProgress(Long userId, User user) {
        UserLevelProgress progress = new UserLevelProgress();

        // 根据用户的交易量、推荐数量等计算积分
        BigDecimal tradeVolume = user.getTotalTradeVolume() != null ? user.getTotalTradeVolume() : BigDecimal.ZERO;
        Long referralCount = getReferralCount(userId);

        // 计算当前积分（交易量/1000 + 推荐数量*10）
        int currentScore = tradeVolume.divide(new BigDecimal("1000"), 0, RoundingMode.DOWN).intValue()
                          + referralCount.intValue() * 10;

        // 根据等级设置下一等级所需积分
        int nextLevelScore;
        switch (user.getUserLevel()) {
            case "NORMAL":
                nextLevelScore = 1000; // VIP需要1000积分
                break;
            case "VIP":
                nextLevelScore = 5000; // PREMIUM需要5000积分
                break;
            case "PREMIUM":
                nextLevelScore = currentScore; // 已是最高等级
                break;
            default:
                nextLevelScore = 1000;
        }

        progress.setCurrentScore(currentScore);
        progress.setNextLevelScore(nextLevelScore);

        if (nextLevelScore > currentScore) {
            BigDecimal percentage = new BigDecimal(currentScore)
                .divide(new BigDecimal(nextLevelScore), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
            progress.setProgressPercentage(percentage);
        } else {
            progress.setProgressPercentage(new BigDecimal("100.00"));
        }

        return progress;
    }

    private Long getReferralCount(Long userId) {
        try {
            Map<String, Object> stats = (Map<String, Object>) referralMapper.getReferralStatistics(userId);
            return stats.get("totalReferrals") != null ? ((Number) stats.get("totalReferrals")).longValue() : 0L;
        } catch (Exception e) {
            log.error("获取推荐数量失败", e);
            return 0L;
        }
    }

    private List<String> getCurrentLevelBenefits(String level) {
        List<String> benefits = new ArrayList<>();
        benefits.add("基础交易功能");
        benefits.add("7x24小时客服支持");

        if ("VIP".equals(level)) {
            benefits.add("VIP专属交易通道");
            benefits.add("优先客服支持");
            benefits.add("高级数据分析");
            benefits.add("更低手续费率");
        } else if ("PREMIUM".equals(level)) {
            benefits.add("VIP专属交易通道");
            benefits.add("优先客服支持");
            benefits.add("高级数据分析");
            benefits.add("最低手续费率");
            benefits.add("专属客户经理");
            benefits.add("高级API权限");
            benefits.add("优先新产品体验");
        }

        return benefits;
    }

    private List<String> getNextLevelBenefits(String nextLevel) {
        List<String> benefits = new ArrayList<>();

        if ("VIP".equals(nextLevel)) {
            benefits.add("VIP专属交易通道");
            benefits.add("优先客服支持");
            benefits.add("高级数据分析");
            benefits.add("更低手续费率");
        } else if ("PREMIUM".equals(nextLevel)) {
            benefits.add("最低手续费率");
            benefits.add("专属客户经理");
            benefits.add("高级API权限");
            benefits.add("优先新产品体验");
        } else {
            benefits.add("更低交易手续费");
            benefits.add("更高提现限额");
            benefits.add("专属客户经理");
        }

        return benefits;
    }

    // 内部类：用户等级进度
    private static class UserLevelProgress {
        private Integer currentScore;
        private Integer nextLevelScore;
        private BigDecimal progressPercentage;

        // getters and setters
        public Integer getCurrentScore() { return currentScore; }
        public void setCurrentScore(Integer currentScore) { this.currentScore = currentScore; }
        public Integer getNextLevelScore() { return nextLevelScore; }
        public void setNextLevelScore(Integer nextLevelScore) { this.nextLevelScore = nextLevelScore; }
        public BigDecimal getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(BigDecimal progressPercentage) { this.progressPercentage = progressPercentage; }
    }
}