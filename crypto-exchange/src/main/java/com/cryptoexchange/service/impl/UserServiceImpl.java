package com.cryptoexchange.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.AdminUserQueryRequest;
import com.cryptoexchange.dto.request.KycReviewRequest;
import com.cryptoexchange.dto.request.UserNotificationQueryRequest;
import com.cryptoexchange.dto.response.AdminUserResponse;
import com.cryptoexchange.dto.response.CheckInRecordResponse;
import com.cryptoexchange.dto.response.CheckInResponse;
import com.cryptoexchange.dto.response.UnreadNotificationCountResponse;
import com.cryptoexchange.dto.response.UserNotificationResponse;
import com.cryptoexchange.dto.response.UserStatisticsResponse;
import com.cryptoexchange.dto.response.UserTradingOverviewResponse;
import com.cryptoexchange.dto.response.UserAssetOverviewResponse;
import com.cryptoexchange.dto.response.UserPermissionsResponse;
import com.cryptoexchange.dto.response.ReferralUserResponse;
import com.cryptoexchange.dto.response.ReferralInfoResponse;
import com.cryptoexchange.dto.response.UserLevelResponse;
import com.cryptoexchange.dto.response.LoginHistoryResponse;
import com.cryptoexchange.dto.response.AvatarUploadResponse;
import com.cryptoexchange.dto.response.KycStatusResponse;
import com.cryptoexchange.dto.response.SecuritySettingsResponse;
import com.cryptoexchange.dto.response.UserProfileResponse;
import com.cryptoexchange.dto.request.SetTradingPasswordRequest;
import com.cryptoexchange.dto.request.ChangePasswordRequest;
import com.cryptoexchange.dto.request.UpdateUserProfileRequest;
import com.cryptoexchange.dto.request.UpdateSecuritySettingsRequest;
import com.cryptoexchange.dto.request.KycApplicationRequest;
import com.cryptoexchange.dto.request.RealNameAuthRequest;
import com.cryptoexchange.dto.request.OperationLogQueryRequest;
import com.cryptoexchange.dto.request.SetupTwoFactorAuthRequest;
import com.cryptoexchange.dto.request.BindPhoneRequest;
import com.cryptoexchange.dto.request.BindEmailRequest;
import com.cryptoexchange.dto.response.TwoFactorAuthResponse;
import com.cryptoexchange.dto.response.OperationLogResponse;
import org.springframework.web.multipart.MultipartFile;
import com.cryptoexchange.entity.User;
import com.cryptoexchange.exception.BusinessException;
import com.cryptoexchange.mapper.UserMapper;
import com.cryptoexchange.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    public User findByReferralCode(String referralCode) {
        return userMapper.findByReferralCode(referralCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (existsByUsername(user.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (existsByEmail(user.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }
        
        // 检查手机号是否已存在（如果提供）
        if (StringUtils.hasText(user.getPhone()) && existsByPhone(user.getPhone())) {
            throw new BusinessException("手机号已存在");
        }
        
        // 生成唯一推荐码
        if (!StringUtils.hasText(user.getReferralCode())) {
            user.setReferralCode(generateUniqueReferralCode());
        }
        
        // 设置默认值
        user.setStatus(1); // 正常状态
        user.setUserType(0); // 普通用户
        user.setKycStatus(0); // 未认证
        user.setTwoFactorEnabled(false);
        user.setFeeLevel(0);
        user.setTotalTradeVolume(BigDecimal.ZERO);
        user.setDeleted(0);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        // 保存用户
        save(user);
        
        log.info("用户创建成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(User user) {
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户信息更新成功: {}", user.getId());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, Integer status) {
        int result = userMapper.updateUserStatus(userId, status, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或状态更新失败");
        }
        log.info("用户状态更新成功: userId={}, status={}", userId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long userId, String newPassword) {
        String encodedPassword = passwordEncoder.encode(newPassword);
        int result = userMapper.updatePassword(userId, encodedPassword, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或密码更新失败");
        }
        log.info("用户密码更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTradingPassword(Long userId, String tradingPassword) {
        String encodedPassword = passwordEncoder.encode(tradingPassword);
        int result = userMapper.updateTradingPassword(userId, encodedPassword, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或交易密码更新失败");
        }
        log.info("用户交易密码更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AvatarUploadResponse uploadAvatar(Long userId, MultipartFile file) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 这里需要实现文件上传逻辑，例如上传到OSS或本地存储
        // 假设文件上传成功后返回可访问的URL
        String avatarUrl = "/path/to/uploaded/avatar.jpg"; // 替换为实际的上传逻辑

        user.setAvatar(avatarUrl);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户头像更新成功: userId={}, avatarUrl={}", userId, avatarUrl);

        return new AvatarUploadResponse(avatarUrl, "avatar.jpg", 0L, "image/jpeg");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNickname(Long userId, String nickname) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setNickname(nickname);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户昵称更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLanguage(Long userId, String language) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setLanguage(language);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户语言偏好更新成功: userId={}, language={}", userId, language);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTimezone(Long userId, String timezone) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setTimezone(timezone);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户时区更新成功: userId={}, timezone={}", userId, timezone);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTwoFactorAuth(Long userId, Boolean enabled) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setTwoFactorEnabled(enabled);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户两步验证更新成功: userId={}, enabled={}", userId, enabled);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateKycStatus(Long userId, Integer kycStatus) {
        int result = userMapper.updateKycStatus(userId, kycStatus, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或KYC状态更新失败");
        }
        log.info("用户KYC状态更新成功: userId={}, kycStatus={}", userId, kycStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRealNameInfo(Long userId, String realName, String idNumber) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setRealName(realName);
        user.setIdCard(idNumber);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户实名信息更新成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFeeLevel(Long userId, Integer feeLevel) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        user.setFeeLevel(feeLevel);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        log.info("用户手续费等级更新成功: userId={}, feeLevel={}", userId, feeLevel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTradingVolume(Long userId, BigDecimal volume) {
        int result = userMapper.updateTradingVolume(userId, volume, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或交易量更新失败");
        }
        log.info("用户交易量更新成功: userId={}, volume={}", userId, volume);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginInfo(Long userId, String loginIp, String userAgent) {
        int result = userMapper.updateLastLoginInfo(userId, loginIp, LocalDateTime.now(), userAgent);
        if (result == 0) {
            throw new BusinessException("用户不存在或登录信息更新失败");
        }
        log.info("用户登录信息更新成功: userId={}, loginIp={}", userId, loginIp);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.countByUsername(username) > 0;
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.countByEmail(email) > 0;
    }

    @Override
    public boolean existsByPhone(String phone) {
        return userMapper.countByPhone(phone) > 0;
    }

    @Override
    public boolean existsByReferralCode(String referralCode) {
        return userMapper.countByReferralCode(referralCode) > 0;
    }

    @Override
    public boolean userExists(Long userId) {
        return userMapper.existsById(userId);
    }

    @Override
    public String generateUniqueReferralCode() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        String referralCode;
        
        do {
            StringBuilder sb = new StringBuilder(6);
            for (int i = 0; i < 6; i++) {
                sb.append(characters.charAt(random.nextInt(characters.length())));
            }
            referralCode = sb.toString();
        } while (existsByReferralCode(referralCode));
        
        return referralCode;
    }

    @Override
    public PageResult<User> findUsers(Integer page, Integer size, String keyword, 
                                     Integer status, Integer userType, Integer kycStatus) {
        Page<User> pageObj = new Page<>(page, size);
        IPage<User> result = userMapper.findUsers(pageObj, keyword, status, userType, kycStatus);
        return PageResult.of(result);
    }

    @Override
    public PageResult<User> findReferralUsers(Long userId, Integer page, Integer size) {
        Page<User> pageObj = new Page<>(page, size);
        IPage<User> result = userMapper.findReferralUsers(pageObj, userId);
        return PageResult.of(result);
    }

    @Override
    public Map<String, Object> getUserStatistics(Long userId) {
        Map<String, Object> statistics = userMapper.getUserStatistics(userId);
        if (statistics == null) {
            statistics = new HashMap<>();
        }
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void softDeleteUser(Long userId) {
        int result = userMapper.softDeleteUser(userId, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或删除失败");
        }
        log.info("用户软删除成功: userId={}", userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreUser(Long userId) {
        int result = userMapper.restoreUser(userId, LocalDateTime.now());
        if (result == 0) {
            throw new BusinessException("用户不存在或恢复失败");
        }
        log.info("用户恢复成功: userId={}", userId);
    }

    @Override
    public boolean verifyPassword(Long userId, String rawPassword) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    @Override
    public boolean verifyTradingPassword(Long userId, String rawTradingPassword) {
        User user = getById(userId);
        if (user == null || user.getTradePassword() == null) {
            return false;
        }
        return passwordEncoder.matches(rawTradingPassword, user.getTradePassword());
    }

    @Override
    public Result<UserStatisticsResponse> getUserStatistics(String period) {
        // 实现用户统计功能
        UserStatisticsResponse response = new UserStatisticsResponse();
        // 这里可以根据period参数获取不同时间段的统计数据
        // 暂时返回空的响应对象
        return Result.success(response);
    }

    @Override
    public Result<Void> reviewKyc(Long kycId, KycReviewRequest request) {
        // 实现KYC审核功能
        // 这里可以根据request参数进行KYC审核
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public Result<Void> freezeUser(Long userId, String reason) {
        // 实现用户冻结功能
        // 这里可以根据userId和reason参数冻结用户
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public Result<Void> unfreezeUser(Long userId) {
        // 实现用户解冻功能
        // 这里可以根据userId参数解冻用户
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public PageResult<AdminUserResponse> getUsers(AdminUserQueryRequest request) {
        // 实现管理员用户查询功能
        // 这里可以根据request参数查询用户列表
        // 暂时返回空的分页结果
        return PageResult.empty(1L, 10L);
    }

    @Override
    public Result<Void> batchMarkNotificationsAsRead(Long userId, List<Long> notificationIds) {
        // 实现批量标记通知为已读功能
        // 这里可以根据userId和notificationIds参数批量标记通知
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public PageResult<UserNotificationResponse> getUserNotifications(UserNotificationQueryRequest request) {
        // 实现用户通知查询功能
        // 这里可以根据request参数查询用户通知列表
        // 暂时返回空的分页结果
        return PageResult.empty(1L, 10L);
    }

    @Override
    public Result<UnreadNotificationCountResponse> getUnreadNotificationCount(Long userId) {
        // 实现获取未读通知数量功能
        // 这里可以根据userId参数查询用户未读通知数量
        // 暂时返回0个未读通知
        UnreadNotificationCountResponse response = new UnreadNotificationCountResponse();
        response.setTotalUnreadCount(0);
        return Result.success(response);
    }

    @Override
    public Result<Void> markNotificationAsRead(Long userId, Long notificationId) {
        // 实现标记通知为已读功能
        // 这里可以根据userId和notificationId参数标记指定通知为已读
        // 暂时返回成功响应
        return Result.success();
    }

    @Override
    public Result<CheckInRecordResponse> getCheckInRecords(Long userId, Integer pageNum, Integer pageSize) {
        // 实现获取签到记录功能
        // 这里可以根据userId、pageNum和pageSize参数查询用户签到记录
        // 暂时返回空的签到记录响应
        CheckInRecordResponse response = new CheckInRecordResponse();
        return Result.success(response);
    }

    @Override
    public Result<CheckInResponse> checkIn(Long userId) {
        // 实现用户签到功能
        // 这里可以根据userId参数执行用户签到操作
        // 暂时返回空的签到响应
        CheckInResponse response = new CheckInResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserTradingOverviewResponse> getUserTradingOverview(Long userId) {
        // 实现获取用户交易概览功能
        // 这里可以根据userId参数查询用户交易概览信息
        // 暂时返回空的交易概览响应
        UserTradingOverviewResponse response = new UserTradingOverviewResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserAssetOverviewResponse> getUserAssetOverview(Long userId) {
        // 实现获取用户资产概览功能
        // 这里可以根据userId参数查询用户资产概览信息
        // 暂时返回空的资产概览响应
        UserAssetOverviewResponse response = new UserAssetOverviewResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserPermissionsResponse> getUserPermissions(Long userId) {
        // 实现获取用户权限功能
        // 这里可以根据userId参数查询用户权限信息
        // 暂时返回空的权限响应
        UserPermissionsResponse response = new UserPermissionsResponse();
        return Result.success(response);
    }

    @Override
    public Result<ReferralUserResponse> getReferralUsers(Long userId, Integer pageNum, Integer pageSize) {
        // 实现获取推荐用户功能
        // 这里可以根据userId、pageNum和pageSize参数查询推荐用户列表
        // 暂时返回空的推荐用户响应
        ReferralUserResponse response = new ReferralUserResponse();
        return Result.success(response);
    }

    @Override
    public Result<ReferralInfoResponse> getReferralInfo(Long userId) {
        // 实现获取推荐信息功能
        // 这里可以根据userId参数查询用户推荐信息
        // 暂时返回空的推荐信息响应
        ReferralInfoResponse response = new ReferralInfoResponse();
        return Result.success(response);
    }

    @Override
    public Result<UserLevelResponse> getUserLevel(Long userId) {
        // 实现获取用户等级功能
        // 这里可以根据userId参数查询用户等级信息
        // 暂时返回空的用户等级响应
        UserLevelResponse response = new UserLevelResponse();
        return Result.success(response);
    }

    @Override
    public Result<LoginHistoryResponse> getLoginHistory(Long userId, Integer pageNum, Integer pageSize) {
        // 实现获取登录历史功能
        // 这里可以根据userId、pageNum和pageSize参数查询用户登录历史
        // 暂时返回空的登录历史响应
        LoginHistoryResponse response = new LoginHistoryResponse();
        return Result.success(response);
    }



    @Override
    public Result<KycStatusResponse> getKycStatus(Long userId) {
        // 实现获取KYC状态功能
        // 这里可以根据userId参数查询用户KYC状态
        // 暂时返回空的KYC状态响应
        KycStatusResponse response = new KycStatusResponse();
        return Result.success(response);
    }

    @Override
    public Result<SecuritySettingsResponse> getSecuritySettings(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        SecuritySettingsResponse response = new SecuritySettingsResponse();
        response.setHasTwoFactorAuth(user.getTwoFactorEnabled());
        response.setHasPhone(StringUtils.hasText(user.getPhone()));
        response.setHasEmail(StringUtils.hasText(user.getEmail()));
        response.setHasTradingPassword(StringUtils.hasText(user.getTradePassword()));
        // 设置脱敏的邮箱和手机号
        if (StringUtils.hasText(user.getEmail())) {
            response.setMaskedEmail(maskEmail(user.getEmail()));
        }
        if (StringUtils.hasText(user.getPhone())) {
            response.setMaskedPhone(maskPhone(user.getPhone()));
        }
        
        log.info("获取用户安全设置成功: userId={}", userId);
        return Result.success(response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setTradingPassword(Long userId, SetTradingPasswordRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证新密码格式
        if (!StringUtils.hasText(request.getTradingPassword()) || request.getTradingPassword().length() < 6) {
            throw new BusinessException("交易密码长度不能少于6位");
        }

        // 确认密码验证
        if (!request.getTradingPassword().equals(request.getConfirmTradingPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 加密并保存新交易密码
        String encodedPassword = passwordEncoder.encode(request.getTradingPassword());
        user.setTradePassword(encodedPassword);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户设置交易密码成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> changePassword(Long userId, ChangePasswordRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证原密码
        if (!StringUtils.hasText(request.getCurrentPassword()) ||
            !passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new BusinessException("原密码错误");
        }
        
        // 验证新密码格式
        if (!StringUtils.hasText(request.getNewPassword()) || request.getNewPassword().length() < 8) {
            throw new BusinessException("新密码长度不能少于8位");
        }
        
        // 确认密码验证
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 新密码不能与原密码相同
        if (passwordEncoder.matches(request.getNewPassword(), user.getPassword())) {
            throw new BusinessException("新密码不能与原密码相同");
        }
        
        // 加密并保存新密码
        String encodedPassword = passwordEncoder.encode(request.getNewPassword());
        user.setPassword(encodedPassword);
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户修改密码成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateUserProfile(Long userId, UpdateUserProfileRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 更新昵称
        if (StringUtils.hasText(request.getNickname())) {
            user.setNickname(request.getNickname());
        }
        
        // 更新头像
        if (StringUtils.hasText(request.getAvatarUrl())) {
            user.setAvatar(request.getAvatarUrl());
        }
        
        // 注意：User实体类中没有bio字段，如果需要可以添加到数据库和实体类中
        
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户资料更新成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    public Result<UserProfileResponse> getUserProfile(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        UserProfileResponse response = new UserProfileResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setNickname(user.getNickname());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setAvatarUrl(user.getAvatar());
        response.setLanguage(user.getLanguage());
        response.setTimezone(user.getTimezone());
        // response.setBio(user.getBio()); // User实体类中没有bio字段
        response.setRealName(user.getRealName());
        response.setKycStatus(user.getKycStatus().toString());
        response.setUserLevel(user.getUserType().toString()); // 使用userLevel字段
        response.setStatus(user.getStatus().toString());
        response.setRegisterTime(user.getCreateTime()); // 使用registerTime字段
        response.setReferralCode(user.getReferralCode());
        
        log.info("获取用户资料成功: userId={}", userId);
        return Result.success(response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateSecuritySettings(Long userId, UpdateSecuritySettingsRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 更新邮箱通知设置
        if (request.getEmailNotificationEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新邮箱通知设置: {}", request.getEmailNotificationEnabled());
        }

        // 更新短信通知设置
        if (request.getSmsNotificationEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新短信通知设置: {}", request.getSmsNotificationEnabled());
        }

        // 更新登录保护设置
        if (request.getLoginProtectionEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新登录保护设置: {}", request.getLoginProtectionEnabled());
        }

        // 更新交易保护设置
        if (request.getTradingProtectionEnabled() != null) {
            // User实体类中没有对应字段，可以根据需要添加
            log.info("更新交易保护设置: {}", request.getTradingProtectionEnabled());
        }
        
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户安全设置更新成功: userId={}", userId);
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> bindPhone(Long userId, BindPhoneRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证手机号格式
        if (!StringUtils.hasText(request.getPhone()) || !isValidPhoneNumber(request.getPhone())) {
            throw new BusinessException("手机号格式不正确");
        }
        
        // 检查手机号是否已被其他用户绑定
        if (existsByPhone(request.getPhone())) {
            throw new BusinessException("该手机号已被其他用户绑定");
        }
        
        // 验证短信验证码
        if (!StringUtils.hasText(request.getVerificationCode())) {
            throw new BusinessException("请输入短信验证码");
        }
        
        // 这里应该验证短信验证码的有效性
        if (!verifySmsCode(request.getPhone(), request.getVerificationCode())) {
            throw new BusinessException("短信验证码错误或已过期");
        }
        
        // 绑定手机号
        user.setPhone(request.getPhone());
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户绑定手机号成功: userId={}, phone={}", userId, request.getPhone());
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> bindEmail(Long userId, BindEmailRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证邮箱格式
        if (!StringUtils.hasText(request.getEmail()) || !isValidEmail(request.getEmail())) {
            throw new BusinessException("邮箱格式不正确");
        }
        
        // 检查邮箱是否已被其他用户绑定
        if (existsByEmail(request.getEmail())) {
            throw new BusinessException("该邮箱已被其他用户绑定");
        }
        
        // 验证邮箱验证码
        if (!StringUtils.hasText(request.getVerificationCode())) {
            throw new BusinessException("请输入邮箱验证码");
        }
        
        // 这里应该验证邮箱验证码的有效性
        if (!verifyEmailCode(request.getEmail(), request.getVerificationCode())) {
            throw new BusinessException("邮箱验证码错误或已过期");
        }
        
        // 绑定邮箱
        user.setEmail(request.getEmail());
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户绑定邮箱成功: userId={}, email={}", userId, request.getEmail());
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> applyKyc(Long userId, KycApplicationRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户是否已经通过KYC认证
        if (user.getKycStatus() != null && user.getKycStatus() == 2) {
            throw new BusinessException("用户已通过KYC认证");
        }
        
        // 检查是否有待审核的KYC申请
        if (user.getKycStatus() != null && user.getKycStatus() == 1) {
            throw new BusinessException("已有KYC申请正在审核中");
        }
        
        // 验证必填信息
        if (!StringUtils.hasText(request.getRealName())) {
            throw new BusinessException("请填写真实姓名");
        }
        
        if (!StringUtils.hasText(request.getIdNumber())) {
            throw new BusinessException("请填写身份证号码");
        }
        
        if (!StringUtils.hasText(request.getIdCardFrontUrl())) {
            throw new BusinessException("请上传身份证正面照片");
        }
        
        if (!StringUtils.hasText(request.getIdCardBackUrl())) {
            throw new BusinessException("请上传身份证反面照片");
        }
        
        // 更新用户KYC信息
        user.setRealName(request.getRealName());
        user.setIdCard(request.getIdNumber());
        user.setKycStatus(1); // 设置为审核中
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        // 这里可以保存KYC申请的详细信息到专门的KYC表中
        // saveKycApplication(userId, request);
        
        log.info("用户提交KYC申请成功: userId={}, realName={}", userId, request.getRealName());
        return Result.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> realNameAuth(Long userId, RealNameAuthRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查用户是否已经实名认证
        if (StringUtils.hasText(user.getRealName()) && StringUtils.hasText(user.getIdCard())) {
            throw new BusinessException("用户已完成实名认证");
        }
        
        // 验证必填信息
        if (!StringUtils.hasText(request.getRealName())) {
            throw new BusinessException("请填写真实姓名");
        }
        
        if (!StringUtils.hasText(request.getIdCardNumber())) {
            throw new BusinessException("请填写身份证号码");
        }

        // 验证身份证号码格式
        if (!isValidIdNumber(request.getIdCardNumber())) {
            throw new BusinessException("身份证号码格式不正确");
        }

        // 检查身份证号是否已被其他用户使用
        if (isIdNumberExists(request.getIdCardNumber(), userId)) {
            throw new BusinessException("该身份证号已被其他用户使用");
        }

        // 更新用户实名信息
        user.setRealName(request.getRealName());
        user.setIdCard(request.getIdCardNumber());
        user.setUpdateTime(LocalDateTime.now());
        updateById(user);
        
        log.info("用户实名认证成功: userId={}, realName={}", userId, request.getRealName());
        return Result.success(null);
    }

    @Override
    public PageResult<OperationLogResponse> getOperationLogs(OperationLogQueryRequest request) {
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据用户ID查询
        if (request.getUserId() != null) {
            queryWrapper.eq(User::getId, request.getUserId());
        }
        
        // 根据操作类型查询
        if (StringUtils.hasText(request.getOperationType())) {
            // 这里应该查询专门的操作日志表，暂时模拟返回
        }
        
        // 根据时间范围查询
        if (request.getStartTime() != null) {
            queryWrapper.ge(User::getCreateTime, request.getStartTime());
        }
        
        if (request.getEndTime() != null) {
            queryWrapper.le(User::getCreateTime, request.getEndTime());
        }
        
        // 创建分页对象
        Page<User> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        // 这里应该查询专门的操作日志表，暂时返回空结果
        // 在实际项目中，应该有专门的OperationLog实体和对应的Mapper
        
        log.info("查询操作日志: userId={}, operationType={}", request.getUserId(), request.getOperationType());
        return PageResult.empty((long)request.getPageNum(), (long)request.getPageSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TwoFactorAuthResponse setupTwoFactorAuth(Long userId, SetupTwoFactorAuthRequest request) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 生成密钥
        String secret = generateTwoFactorSecret();
        
        // 构建QR码URL
        String qrCodeUrl = String.format(
            "otpauth://totp/CryptoExchange:%s?secret=%s&issuer=CryptoExchange",
            user.getEmail() != null ? user.getEmail() : user.getUsername(),
            secret
        );
        
        // 验证用户提供的验证码（如果有）
        if (StringUtils.hasText(request.getVerificationCode())) {
            if (!verifyTwoFactorCode(secret, request.getVerificationCode())) {
                throw new BusinessException("验证码错误，请重新输入");
            }
            
            // 验证成功，启用双因子认证
            user.setTwoFactorEnabled(true);
            user.setTwoFactorSecret(secret);
            user.setUpdateTime(LocalDateTime.now());
            updateById(user);
            
            log.info("用户启用双因子认证成功: userId={}", userId);
        }
        
        TwoFactorAuthResponse response = new TwoFactorAuthResponse();
        response.setSecret(secret);
        response.setQrCodeUrl(qrCodeUrl);
        response.setEnabled(user.getTwoFactorEnabled());
        response.setAuthType(request.getAuthType());
        
        return response;
    }

    @Override
    public boolean verifyTwoFactorAuth(Long userId, String code) {
        User user = getById(userId);
        if (user == null) {
            log.warn("验证双因子认证失败: 用户不存在, userId={}", userId);
            return false;
        }
        
        // 检查用户是否启用了双因子认证
        if (!user.getTwoFactorEnabled() || !StringUtils.hasText(user.getTwoFactorSecret())) {
            log.warn("验证双因子认证失败: 用户未启用双因子认证, userId={}", userId);
            return false;
        }
        
        // 验证验证码
        if (!StringUtils.hasText(code)) {
            log.warn("验证双因子认证失败: 验证码为空, userId={}", userId);
            return false;
        }
        
        boolean isValid = verifyTwoFactorCode(user.getTwoFactorSecret(), code);
        
        if (isValid) {
            log.info("双因子认证验证成功: userId={}", userId);
        } else {
            log.warn("双因子认证验证失败: 验证码错误, userId={}", userId);
        }
        
        return isValid;
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 验证手机号格式
     */
    private boolean isValidPhoneNumber(String phone) {
        // 简单的手机号验证，实际项目中可以使用更复杂的正则表达式
        return phone != null && phone.matches("^1[3-9]\\d{9}$");
    }
    
    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        // 简单的邮箱验证，实际项目中可以使用更复杂的正则表达式
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    /**
     * 验证身份证号码格式
     */
    private boolean isValidIdNumber(String idNumber) {
        // 简单的身份证号验证，实际项目中可以使用更复杂的验证逻辑
        return idNumber != null && idNumber.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    }
    
    /**
     * 检查身份证号是否已存在
     */
    private boolean isIdNumberExists(String idNumber, Long excludeUserId) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getIdCard, idNumber);
        if (excludeUserId != null) {
            queryWrapper.ne(User::getId, excludeUserId);
        }
        return count(queryWrapper) > 0;
    }
    
    /**
     * 验证短信验证码
     */
    private boolean verifySmsCode(String phone, String code) {
        // 这里应该调用短信服务提供商的API来验证验证码
        // 暂时模拟验证逻辑
        log.info("验证短信验证码: phone={}, code={}", phone, code);
        return "123456".equals(code); // 模拟验证码
    }
    
    /**
     * 验证邮箱验证码
     */
    private boolean verifyEmailCode(String email, String code) {
        // 这里应该调用邮件服务来验证验证码
        // 暂时模拟验证逻辑
        log.info("验证邮箱验证码: email={}, code={}", email, code);
        return "123456".equals(code); // 模拟验证码
    }
    
    /**
     * 生成双因子认证密钥
     */
    private String generateTwoFactorSecret() {
        // 生成32位随机密钥
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        Random random = new Random();
        StringBuilder secret = new StringBuilder(32);
        
        for (int i = 0; i < 32; i++) {
            secret.append(characters.charAt(random.nextInt(characters.length())));
        }
        
        return secret.toString();
    }
    
    /**
     * 验证双因子认证码
     */
    private boolean verifyTwoFactorCode(String secret, String code) {
        // 这里应该使用TOTP算法来验证验证码
        // 暂时模拟验证逻辑
        log.info("验证双因子认证码: secret={}, code={}", secret, code);

        // 简单模拟：如果验证码是6位数字，则认为有效
        return code != null && code.matches("^\\d{6}$");
    }

    /**
     * 脱敏邮箱地址
     */
    private String maskEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return "";
        }
        int atIndex = email.indexOf('@');
        if (atIndex <= 1) {
            return email;
        }
        String prefix = email.substring(0, 1) + "***" + email.substring(atIndex - 1);
        return prefix;
    }

    /**
     * 脱敏手机号
     */
    private String maskPhone(String phone) {
        if (!StringUtils.hasText(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
}