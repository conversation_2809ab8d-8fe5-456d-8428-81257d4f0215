package com.cryptoexchange.service.impl;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.entity.*;
import com.cryptoexchange.exception.BusinessException;
import com.cryptoexchange.mapper.*;
import com.cryptoexchange.service.FuturesService;
import com.cryptoexchange.service.NotificationService;
import com.cryptoexchange.service.UserService;
import com.cryptoexchange.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import org.springframework.util.StringUtils;
import java.math.RoundingMode;

/**
 * 合约交易服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FuturesServiceImpl implements FuturesService {

    @Autowired
    private FuturesContractMapper futuresContractMapper;
    
    @Autowired
    private FuturesOrderMapper futuresOrderMapper;
    
    @Autowired
    private FuturesPositionMapper futuresPositionMapper;
    
    @Autowired
    private FuturesTradeMapper futuresTradeMapper;
    
    @Autowired
    private MarginAccountMapper marginAccountMapper;
    
    @Autowired
    private UserWalletMapper userWalletMapper;
    
    @Autowired
    private NotificationService notificationService;
    
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FuturesLiquidationMapper futuresLiquidationMapper;

    @Autowired
    private FuturesFundingRateMapper futuresFundingRateMapper;

    @Autowired
    private FuturesFundingFeeMapper futuresFundingFeeMapper;

    @Autowired
    private AssetTransferRecordMapper assetTransferRecordMapper;

    @Autowired
    private SpotAccountMapper spotAccountMapper;

    @Autowired
    private FuturesMarginAccountMapper futuresMarginAccountMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FuturesInsuranceFundMapper futuresInsuranceFundMapper;

    @Autowired
    private FuturesMarkPriceMapper futuresMarkPriceMapper;
    
    // 合约锁，防止并发操作
    private final Map<String, ReentrantLock> contractLocks = new HashMap<>();
    
    @Override
    public Result<List<Object>> getContracts() {
        try {
            List<FuturesContract> contracts = futuresContractMapper.findActiveContracts();
            List<Object> result = new ArrayList<>();
            for (FuturesContract contract : contracts) {
                result.add(contract);
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取合约产品列表失败", e);
            return Result.error("获取合约产品列表失败");
        }
    }
    
    @Override
    public Result<Object> getContract(String symbol) {
        try {
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            return Result.success(contract);
        } catch (Exception e) {
            log.error("获取合约产品详情失败: {}", symbol, e);
            return Result.error("获取合约产品详情失败");
        }
    }
    
    @Override
    public Result<Object> getMarginAccount(Long userId) {
        try {
            MarginAccount account = marginAccountMapper.selectByUserId(userId);
            return Result.success(account);
        } catch (Exception e) {
            log.error("获取用户杠杆账户信息失败: {}", userId, e);
            return Result.error("获取用户杠杆账户信息失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> createOrder(FuturesOrderRequest request) {
        try {
            log.info("创建合约订单，请求参数: {}", request);
            
            // 参数验证
            if (request == null || request.getUserId() == null || request.getSymbol() == null) {
                return Result.error("参数不能为空");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(request.getSymbol());
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 验证用户保证金账户
            MarginAccount marginAccount = marginAccountMapper.selectByUserId(request.getUserId());
            if (marginAccount == null) {
                return Result.error("用户保证金账户不存在");
            }
            
            // 计算所需保证金
            BigDecimal requiredMargin = calculateOrderMargin(request, contract);
            if (marginAccount.getAvailableMargin().compareTo(requiredMargin) < 0) {
                return Result.error("保证金不足");
            }

            // 创建订单
            FuturesOrder order = new FuturesOrder();
            order.setUserId(request.getUserId());
            order.setSymbol(request.getSymbol());
            order.setOrderType(request.getType());
            order.setSide(request.getSide());
            order.setQuantity(request.getQuantity());
            order.setPrice(request.getPrice());
            order.setLeverage(request.getLeverage());
            order.setStatus("PENDING"); // 待成交
            order.setCreateTime(LocalDateTime.now());
            order.setUpdateTime(LocalDateTime.now());
            
            // 生成订单ID
            String orderId = generateOrderId();
            order.setOrderId(orderId);
            
            // 保存订单
            futuresOrderMapper.insert(order);
            
            // 冻结保证金
            marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().subtract(requiredMargin));
            marginAccount.setFrozenBalance(marginAccount.getFrozenBalance().add(requiredMargin));
            marginAccountMapper.updateById(marginAccount);
            
            log.info("合约订单创建成功，订单ID: {}", orderId);
            return Result.success(order);
            
        } catch (Exception e) {
            log.error("创建合约订单失败", e);
            return Result.error("创建合约订单失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelOrder(Long userId, String orderId) {
        try {
            log.info("取消合约订单，用户ID: {}, 订单ID: {}", userId, orderId);
            
            // 查找订单
            FuturesOrder order = futuresOrderMapper.selectByOrderId(orderId);
            if (order == null) {
                return Result.error("订单不存在");
            }
            
            // 验证订单所有者
            if (!order.getUserId().equals(userId)) {
                return Result.error("无权限取消此订单");
            }
            
            // 检查订单状态
            if (!"PENDING".equals(order.getStatus())) { // PENDING: 待成交
                return Result.error("订单状态不允许取消");
            }

            // 更新订单状态为已取消
            order.setStatus("CANCELLED"); // CANCELLED: 已取消
            order.setUpdateTime(LocalDateTime.now());
            futuresOrderMapper.updateById(order);
            
            // 释放冻结的保证金
            MarginAccount marginAccount = marginAccountMapper.selectByUserId(userId);
            if (marginAccount != null) {
                BigDecimal frozenMargin = calculateOrderMargin(order);
                marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().add(frozenMargin));
                marginAccount.setFrozenBalance(marginAccount.getFrozenBalance().subtract(frozenMargin));
                marginAccountMapper.updateById(marginAccount);
            }
            
            log.info("合约订单取消成功，订单ID: {}", orderId);
            return Result.success();
            
        } catch (Exception e) {
            log.error("取消合约订单失败", e);
            return Result.error("取消合约订单失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelAllOrders(Long userId, String symbol) {
        try {
            log.info("批量取消订单，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 查找用户的待成交订单
            List<FuturesOrder> orders = futuresOrderMapper.selectPendingOrdersByUserAndSymbol(userId, symbol);
            
            if (orders.isEmpty()) {
                return Result.success();
            }
            
            // 获取用户保证金账户
            MarginAccount marginAccount = marginAccountMapper.selectByUserId(userId);
            BigDecimal totalReleasedMargin = BigDecimal.ZERO;
            
            // 批量取消订单
            for (FuturesOrder order : orders) {
                // 更新订单状态为已取消
                order.setStatus("CANCELLED"); // CANCELLED: 已取消
                order.setUpdateTime(LocalDateTime.now());
                futuresOrderMapper.updateById(order);

                // 计算释放的保证金
                BigDecimal frozenMargin = calculateOrderMargin(order);
                totalReleasedMargin = totalReleasedMargin.add(frozenMargin);
            }

            // 释放总的冻结保证金
            if (marginAccount != null && totalReleasedMargin.compareTo(BigDecimal.ZERO) > 0) {
                marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().add(totalReleasedMargin));
                marginAccount.setFrozenBalance(marginAccount.getFrozenBalance().subtract(totalReleasedMargin));
                marginAccountMapper.updateById(marginAccount);
            }
            
            log.info("批量取消订单成功，用户ID: {}, 合约符号: {}, 取消订单数: {}", userId, symbol, orders.size());
            return Result.success();
            
        } catch (Exception e) {
            log.error("批量取消订单失败", e);
            return Result.error("批量取消订单失败");
        }
    }
    
    @Override
    public Result<PageResult<Object>> getUserOrders(Long userId, String symbol, Integer status, Integer pageNum, Integer pageSize) {
        try {
            log.info("获取用户合约订单，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 设置默认分页参数
            if (pageNum == null || pageNum <= 0) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            
            // 构建查询条件
            Map<String, Object> params = new HashMap<>();
            params.put("userId", userId);
            if (symbol != null && !symbol.trim().isEmpty()) {
                params.put("symbol", symbol);
            }
            if (status != null) {
                params.put("status", status);
            }
            params.put("offset", (pageNum - 1) * pageSize);
            params.put("limit", pageSize);
            
            // 查询订单列表
            List<FuturesOrder> orders = futuresOrderMapper.selectOrdersByCondition(params);
            
            // 查询总数
            Long total = futuresOrderMapper.countOrdersByCondition(params);
            
            // 转换为响应对象
            List<Object> orderResponses = new ArrayList<>();
            for (FuturesOrder order : orders) {
                orderResponses.add(convertToOrderResponse(order));
            }
            
            // 构建分页结果
            PageResult<Object> pageResult = new PageResult<>((long) pageNum, (long) pageSize, total, orderResponses);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取用户合约订单失败", e);
            return Result.error("获取用户合约订单失败");
        }
    }
    
    @Override
    public Result<Object> getOrderDetail(Long userId, String orderId) {
        try {
            log.info("获取订单详情，用户ID: {}, 订单ID: {}", userId, orderId);
            
            // 查找订单
            FuturesOrder order = futuresOrderMapper.selectByOrderId(orderId);
            if (order == null) {
                return Result.error("订单不存在");
            }
            
            // 验证订单所有者
            if (!order.getUserId().equals(userId)) {
                return Result.error("无权限查看此订单");
            }
            
            // 查询订单相关的成交记录
            List<FuturesTrade> trades = futuresTradeMapper.selectByOrderId(orderId);
            
            // 构建订单详情响应
            Map<String, Object> orderDetail = new HashMap<>();
            orderDetail.put("orderId", order.getOrderId());
            orderDetail.put("symbol", order.getSymbol());
            orderDetail.put("side", order.getSide());
            orderDetail.put("orderType", order.getOrderType());
            orderDetail.put("quantity", order.getQuantity());
            orderDetail.put("price", order.getPrice());
            orderDetail.put("leverage", order.getLeverage());
            orderDetail.put("status", order.getStatus());
            orderDetail.put("createTime", order.getCreateTime());
            orderDetail.put("updateTime", order.getUpdateTime());
            
            // 计算已成交数量和平均成交价格
            BigDecimal filledQuantity = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;
            
            for (FuturesTrade trade : trades) {
                filledQuantity = filledQuantity.add(trade.getQuantity());
                totalAmount = totalAmount.add(trade.getQuantity().multiply(trade.getPrice()));
            }
            
            orderDetail.put("filledQuantity", filledQuantity);
            orderDetail.put("remainingQuantity", order.getQuantity().subtract(filledQuantity));
            
            if (filledQuantity.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal avgPrice = totalAmount.divide(filledQuantity, 8, BigDecimal.ROUND_HALF_UP);
                orderDetail.put("avgPrice", avgPrice);
            } else {
                orderDetail.put("avgPrice", BigDecimal.ZERO);
            }
            
            orderDetail.put("trades", trades);
            
            return Result.success(orderDetail);
            
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return Result.error("获取订单详情失败");
        }
    }
    
    @Override
    public Result<List<FuturesPositionResponse>> getUserPositions(Long userId, String symbol) {
        try {
            log.info("获取用户持仓，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 构建查询条件
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", userId);
            if (symbol != null && !symbol.trim().isEmpty()) {
                queryParams.put("symbol", symbol);
            }
            
            // 查询用户持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectByUserId(userId);

            // 转换为响应对象
            List<FuturesPositionResponse> positionList = new ArrayList<>();
            for (FuturesPosition position : positions) {
                FuturesPositionResponse positionInfo = new FuturesPositionResponse();
                positionInfo.setSymbol(position.getSymbol());
                positionInfo.setSide(position.getSide());
                positionInfo.setQuantity(position.getQuantity());
                positionInfo.setAvgOpenPrice(position.getAvgPrice());
                positionInfo.setMarkPrice(position.getMarkPrice());
                positionInfo.setLeverage(position.getLeverage());
                positionInfo.setMargin(position.getMargin());
                // 计算保证金率
                BigDecimal marginRatio = calculateMarginRatio(position);
                positionInfo.setRiskRatio(marginRatio);
                
                // 计算未实现盈亏
                BigDecimal unrealizedPnl = calculateUnrealizedPnl(position);
                positionInfo.setUnrealizedPnl(unrealizedPnl);
                
                // 计算收益率
                BigDecimal roe = calculateRoe(position, unrealizedPnl);
                positionInfo.setReturnRate(roe);
                
                // 计算强平价格
                BigDecimal liquidationPrice = calculatePositionLiquidationPrice(position);
                positionInfo.setLiquidationPrice(liquidationPrice);
                
                positionInfo.setCreateTime(position.getCreateTime());
                positionInfo.setUpdateTime(position.getUpdateTime());
                
                positionList.add(positionInfo);
            }
            
            return Result.success(positionList);
            
        } catch (Exception e) {
            log.error("获取用户持仓失败", e);
            return Result.error("获取用户持仓失败");
        }
    }
    
    /**
     * 计算未实现盈亏
     */
    private BigDecimal calculateUnrealizedPnl(FuturesPosition position) {
        // TODO: 实现未实现盈亏计算逻辑
        return BigDecimal.ZERO;
    }
    
    /**
     * 计算收益率
     */
    private BigDecimal calculateRoe(FuturesPosition position, BigDecimal unrealizedPnl) {
        // TODO: 实现收益率计算逻辑
        return BigDecimal.ZERO;
    }
    
    /**
     * 计算持仓强平价格
     */
    private BigDecimal calculatePositionLiquidationPrice(FuturesPosition position) {
        // TODO: 实现强平价格计算逻辑
        return BigDecimal.ZERO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> adjustMargin(Long userId, String symbol, BigDecimal amount, String type) {
        try {
            // TODO: 实现调整持仓保证金逻辑
            log.info("调整持仓保证金，用户ID: {}, 合约符号: {}, 金额: {}, 类型: {}", userId, symbol, amount, type);
            throw new UnsupportedOperationException("方法待实现");
        } catch (Exception e) {
            log.error("调整持仓保证金失败", e);
            return Result.error("调整持仓保证金失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> adjustMargin(Long userId, String symbol, BigDecimal amount, Integer type) {
        try {
            log.info("调整保证金，用户ID: {}, 合约符号: {}, 金额: {}, 类型: {}", userId, symbol, amount, type);
            
            // 参数验证
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("调整金额必须大于0");
            }
            
            if (type == null || (type != 1 && type != 2)) {
                return Result.error("调整类型只能是1(增加)或2(减少)");
            }
            
            // 查找用户持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectByUserIdAndSymbol(userId, symbol);
            if (positions == null || positions.isEmpty()) {
                return Result.error("未找到对应持仓");
            }
            FuturesPosition position = positions.get(0); // 取第一个持仓
            
            // 获取用户保证金账户
            MarginAccount marginAccount = marginAccountMapper.selectByUserId(userId);
            if (marginAccount == null) {
                return Result.error("保证金账户不存在");
            }
            
            ReentrantLock lock = getContractLock(symbol);
            lock.lock();
            
            try {
                if (type == 1) {
                    // 增加保证金
                    if (marginAccount.getAvailableMargin().compareTo(amount) < 0) {
                        return Result.error("可用余额不足");
                    }

                    // 从可用余额转入保证金
                    marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().subtract(amount));
                    
                    // 更新持仓保证金
                    position.setMargin(position.getMargin().add(amount));
                    
                } else {
                    // 减少保证金
                    if (position.getMargin().compareTo(amount) < 0) {
                        return Result.error("持仓保证金不足");
                    }
                    
                    // 计算减少保证金后的风险
                    BigDecimal newMargin = position.getMargin().subtract(amount);
                    BigDecimal minMargin = calculateMinRequiredMargin(position);
                    
                    if (newMargin.compareTo(minMargin) < 0) {
                        return Result.error("减少保证金后将触发强平风险");
                    }
                    
                    // 从保证金转出到可用余额
                    marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().add(amount));
                    
                    // 更新持仓保证金
                    position.setMargin(newMargin);
                }
                
                // 重新计算保证金率
                BigDecimal marginRatio = calculateMarginRatio(position);
                position.setMarginRatio(marginRatio);
                position.setUpdateTime(LocalDateTime.now());
                
                // 更新数据库
                marginAccountMapper.updateById(marginAccount);
                futuresPositionMapper.updateById(position);
                
                // 记录保证金调整日志
                log.info("保证金调整成功，用户ID: {}, 合约符号: {}, 类型: {}, 金额: {}", userId, symbol, type, amount);
                
                Map<String, Object> result = new HashMap<>();
                result.put("symbol", symbol);
                result.put("margin", position.getMargin());
                result.put("marginRatio", marginRatio);
                result.put("availableBalance", marginAccount.getAvailableMargin());
                
                return Result.success(result);
                
            } finally {
                lock.unlock();
            }
            
        } catch (Exception e) {
            log.error("调整保证金失败", e);
            return Result.error("调整保证金失败");
        }
    }
    
    /**
     * 计算最小所需保证金
     */
    private BigDecimal calculateMinRequiredMargin(FuturesPosition position) {
        // 最小保证金 = 持仓价值 / 杠杆倍数 * 维持保证金率
        BigDecimal positionValue = position.getQuantity().multiply(position.getMarkPrice());
        BigDecimal baseMargin = positionValue.divide(new BigDecimal(position.getLeverage()), 8, BigDecimal.ROUND_UP);
        // 假设维持保证金率为50%
        return baseMargin.multiply(new BigDecimal("0.5"));
    }

    /**
     * 计算保证金率
     */
    private BigDecimal calculateMarginRatio(FuturesPosition position) {
        if (position.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal positionValue = position.getQuantity().multiply(position.getMarkPrice());
        if (positionValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return position.getMargin().divide(positionValue, 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算用户指定资产的未实现盈亏
     */
    private BigDecimal calculateUnrealizedPnl(Long userId, String asset) {
        try {
            // 获取用户所有持仓
            List<FuturesPosition> positions = futuresPositionMapper.findByUserId(userId);
            BigDecimal totalUnrealizedPnl = BigDecimal.ZERO;

            for (FuturesPosition position : positions) {
                if (position.getUnrealizedPnl() != null) {
                    totalUnrealizedPnl = totalUnrealizedPnl.add(position.getUnrealizedPnl());
                }
            }

            return totalUnrealizedPnl;
        } catch (Exception e) {
            log.error("计算未实现盈亏失败，用户ID: {}, 资产: {}", userId, asset, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算用户指定资产的保证金率
     */
    private BigDecimal calculateMarginRatio(Long userId, String asset) {
        try {
            // 获取用户保证金账户
            FuturesMarginAccount marginAccount = futuresMarginAccountMapper.findByUserIdAndAsset(userId, asset);
            if (marginAccount == null) {
                return BigDecimal.ZERO;
            }

            // 获取用户所有持仓的总价值
            List<FuturesPosition> positions = futuresPositionMapper.findByUserId(userId);
            BigDecimal totalPositionValue = BigDecimal.ZERO;

            for (FuturesPosition position : positions) {
                if (position.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal markPrice = getMarkPriceValue(position.getSymbol());
                    if (markPrice != null) {
                        BigDecimal positionValue = position.getQuantity().abs().multiply(markPrice);
                        totalPositionValue = totalPositionValue.add(positionValue);
                    }
                }
            }

            if (totalPositionValue.compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }

            // 保证金率 = 保证金 / 持仓价值
            return marginAccount.getBalance().divide(totalPositionValue, 4, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("计算保证金率失败，用户ID: {}, 资产: {}", userId, asset, e);
            return BigDecimal.ZERO;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> adjustLeverage(Long userId, String symbol, Integer leverage) {
        try {
            log.info("调整杠杆倍数，用户ID: {}, 合约符号: {}, 杠杆倍数: {}", userId, symbol, leverage);
            
            // 参数验证
            if (leverage == null || leverage <= 0 || leverage > 100) {
                return Result.error("杠杆倍数必须在1-100之间");
            }
            
            // 查找用户持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectByUserIdAndSymbol(userId, symbol);
            if (positions == null || positions.isEmpty()) {
                return Result.error("未找到对应持仓");
            }
            FuturesPosition position = positions.get(0); // 取第一个持仓
            
            // 检查是否有未成交订单
            List<FuturesOrder> pendingOrders = futuresOrderMapper.selectPendingOrdersByUserAndSymbol(userId, symbol);
            if (!pendingOrders.isEmpty()) {
                return Result.error("存在未成交订单，无法调整杠杆");
            }
            
            // 获取用户保证金账户
            MarginAccount marginAccount = marginAccountMapper.selectByUserId(userId);
            if (marginAccount == null) {
                return Result.error("保证金账户不存在");
            }
            
            ReentrantLock lock = getContractLock(symbol);
            lock.lock();
            
            try {
                Integer oldLeverage = position.getLeverage();
                
                // 计算新的所需保证金
                BigDecimal positionValue = position.getQuantity().multiply(position.getMarkPrice());
                BigDecimal newRequiredMargin = positionValue.divide(new BigDecimal(leverage), 8, BigDecimal.ROUND_UP);
                BigDecimal oldMargin = position.getMargin();

                if (leverage > oldLeverage) {
                    // 提高杠杆，释放保证金
                    BigDecimal releasedMargin = oldMargin.subtract(newRequiredMargin);
                    if (releasedMargin.compareTo(BigDecimal.ZERO) > 0) {
                        marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().add(releasedMargin));
                        position.setMargin(newRequiredMargin);
                    }
                } else {
                    // 降低杠杆，需要更多保证金
                    BigDecimal additionalMargin = newRequiredMargin.subtract(oldMargin);
                    if (additionalMargin.compareTo(BigDecimal.ZERO) > 0) {
                        if (marginAccount.getAvailableMargin().compareTo(additionalMargin) < 0) {
                            return Result.error("可用余额不足，无法降低杠杆");
                        }
                        marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().subtract(additionalMargin));
                        position.setMargin(newRequiredMargin);
                    }
                }

                // 更新杠杆和保证金率
                position.setLeverage(leverage);
                BigDecimal marginRatio = calculateMarginRatio(position);
                // position.setMarginRatio(marginRatio); // FuturesPosition没有这个方法
                position.setUpdateTime(LocalDateTime.now());
                
                // 更新数据库
                marginAccountMapper.updateById(marginAccount);
                futuresPositionMapper.updateById(position);
                
                log.info("杠杆调整成功，用户ID: {}, 合约符号: {}, 从{}倍调整为{}倍", userId, symbol, oldLeverage, leverage);
                
                return Result.success();
                
            } finally {
                lock.unlock();
            }
            
        } catch (Exception e) {
            log.error("调整杠杆倍数失败", e);
            return Result.error("调整杠杆倍数失败");
        }
    }
    
    @Override
    public Result<PageResult<FuturesTradeResponse>> getUserTrades(Long userId, String symbol, Integer pageNum, Integer pageSize) {
        try {
            log.info("获取用户合约交易记录，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 设置默认分页参数
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1 || pageSize > 100) {
                pageSize = 20;
            }
            
            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;
            
            // 构建查询条件
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", userId);
            if (symbol != null && !symbol.trim().isEmpty()) {
                queryParams.put("symbol", symbol);
            }
            queryParams.put("offset", offset);
            queryParams.put("limit", pageSize);
            
            // 查询成交记录
            List<FuturesTrade> trades;
            if (symbol != null && !symbol.trim().isEmpty()) {
                trades = futuresTradeMapper.selectByUserIdAndSymbol(userId, symbol);
            } else {
                trades = futuresTradeMapper.selectByUserId(userId);
            }

            // 手动分页
            int start = offset;
            int end = Math.min(start + pageSize, trades.size());
            List<FuturesTrade> pagedTrades = trades.subList(start, end);

            // 查询总数
            Long total = (long) trades.size();

            // 转换为响应对象
            List<FuturesTradeResponse> tradeList = new ArrayList<>();
            for (FuturesTrade trade : pagedTrades) {
                FuturesTradeResponse tradeInfo = new FuturesTradeResponse();
                tradeInfo.setTradeNo(trade.getTradeId());
                tradeInfo.setBuyOrderId(Long.valueOf(trade.getBuyerOrderId()));
                tradeInfo.setSellOrderId(Long.valueOf(trade.getSellerOrderId()));
                tradeInfo.setSymbol(trade.getSymbol());
                tradeInfo.setSide(trade.getSide());
                tradeInfo.setQuantity(trade.getQuantity());
                tradeInfo.setPrice(trade.getPrice());
                tradeInfo.setBuyFee(trade.getBuyerFee());
                tradeInfo.setSellFee(trade.getSellerFee());
                tradeInfo.setBuyFeeCurrency(trade.getBuyerFeeCurrency());
                tradeInfo.setSellFeeCurrency(trade.getSellerFeeCurrency());
                tradeInfo.setTradeTime(trade.getTradeTime());

                tradeList.add(tradeInfo);
            }

            // 构建分页结果
            PageResult<FuturesTradeResponse> pageResult = new PageResult<>((long) pageNum, (long) pageSize, total, tradeList);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取用户合约交易记录失败", e);
            return Result.error("获取用户合约交易记录失败");
        }
    }
    
    @Override
    public Result<PageResult<LiquidationResponse>> getLiquidationRecords(Long userId, String symbol, Integer pageNum, Integer pageSize) {
        try {
            log.info("获取强平记录，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 设置默认分页参数
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1 || pageSize > 100) {
                pageSize = 20;
            }
            
            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;
            
            // 构建查询条件
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", userId);
            if (symbol != null && !symbol.trim().isEmpty()) {
                queryParams.put("symbol", symbol);
            }
            queryParams.put("offset", offset);
            queryParams.put("limit", pageSize);
            
            // 查询强平记录
            List<FuturesLiquidation> liquidations = futuresLiquidationMapper.selectByUserId(userId, symbol, offset, pageSize);
            
            // 查询总数
            Long total = futuresLiquidationMapper.countByUserId(userId, symbol);
            
            // 转换为响应对象
            List<LiquidationResponse> liquidationList = new ArrayList<>();
            for (FuturesLiquidation liquidation : liquidations) {
                LiquidationResponse liquidationInfo = new LiquidationResponse();
                liquidationInfo.setUserId(liquidation.getUserId());
                liquidationInfo.setSymbol(liquidation.getSymbol());
                // 将字符串转换为Integer：BUY=1, SELL=2
                liquidationInfo.setSide("BUY".equals(liquidation.getSide()) ? 1 : 2);
                liquidationInfo.setQuantity(liquidation.getQuantity());
                liquidationInfo.setPrice(liquidation.getPrice());
                liquidationInfo.setFee(liquidation.getFee());
                liquidationInfo.setLoss(liquidation.getRealizedPnl().negate()); // 损失为负的已实现盈亏
                liquidationInfo.setMargin(liquidation.getMarginReleased());
                // 将字符串转换为Integer：FORCED=1, ADL=2
                liquidationInfo.setLiquidationType("FORCED".equals(liquidation.getLiquidationType()) ? 1 : 2);
                liquidationInfo.setCreateTime(liquidation.getCreateTime());

                liquidationList.add(liquidationInfo);
            }
            
            // 构建分页结果
            PageResult<LiquidationResponse> pageResult = new PageResult<>((long) pageNum, (long) pageSize, total, liquidationList);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取强平记录失败", e);
            return Result.error("获取强平记录失败");
        }
    }
    
    @Override
    public Result<FundingRateResponse> getFundingRate(String symbol) {
        try {
            log.info("获取资金费率，合约符号: {}", symbol);
            
            // 查询当前资金费率
            FuturesFundingRate currentRate = futuresFundingRateMapper.selectCurrentBySymbol(symbol);
            if (currentRate == null) {
                return Result.error("未找到该合约的资金费率信息");
            }
            
            // 构建响应对象
            FundingRateResponse response = new FundingRateResponse();
            response.setSymbol(currentRate.getSymbol());
            response.setFundingRate(currentRate.getFundingRate());
            response.setFundingTime(currentRate.getFundingTime());
            response.setNextFundingTime(currentRate.getNextFundingTime());
            response.setMarkPrice(currentRate.getMarkPrice());
            response.setIndexPrice(currentRate.getIndexPrice());
            response.setInterestRate(currentRate.getInterestRate());
            
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("获取资金费率失败", e);
            return Result.error("获取资金费率失败");
        }
    }
    
    @Override
    public Result<PageResult<UserFundingFeeResponse>> getUserFundingFees(Long userId, String symbol, Integer pageNum, Integer pageSize) {
        try {
            log.info("获取用户资金费用记录，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 设置默认分页参数
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1 || pageSize > 100) {
                pageSize = 20;
            }
            
            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;
            
            // 查询用户资金费用记录
            List<FuturesFundingFee> fundingFees = futuresFundingFeeMapper.selectByUserId(userId, symbol, offset, pageSize);
            
            // 查询总数
            Long total = futuresFundingFeeMapper.countByUserId(userId, symbol);
            
            // 转换为响应对象
            List<UserFundingFeeResponse> feeList = new ArrayList<>();
            for (FuturesFundingFee fee : fundingFees) {
                UserFundingFeeResponse feeInfo = new UserFundingFeeResponse();
                feeInfo.setSymbol(fee.getSymbol());
                feeInfo.setFundingRate(fee.getFundingRate());
                feeInfo.setFundingFee(fee.getFundingFee());
                feeInfo.setPositionSize(fee.getPositionSize());
                feeInfo.setMarkPrice(fee.getMarkPrice());
                feeInfo.setFundingTime(fee.getFundingTime());
                feeInfo.setCreateTime(fee.getCreateTime());
                
                feeList.add(feeInfo);
            }
            
            // 构建分页结果
            PageResult<UserFundingFeeResponse> pageResult = new PageResult<>((long) pageNum, (long) pageSize, total, feeList);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取用户资金费用记录失败", e);
            return Result.error("获取用户资金费用记录失败");
        }
    }
    
    @Override
    public Result<BigDecimal> calculateRequiredMargin(String symbol, BigDecimal quantity, BigDecimal price, Integer leverage) {
        try {
            log.info("计算开仓所需保证金，合约符号: {}, 数量: {}, 价格: {}, 杠杆: {}", symbol, quantity, price, leverage);
            
            // 参数验证
            if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("数量必须大于0");
            }
            if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("价格必须大于0");
            }
            if (leverage == null || leverage <= 0 || leverage > 100) {
                return Result.error("杠杆倍数必须在1-100之间");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 计算名义价值
            BigDecimal notionalValue = quantity.multiply(price);
            
            // 计算所需保证金 = 名义价值 / 杠杆倍数
            BigDecimal requiredMargin = notionalValue.divide(new BigDecimal(leverage), 8, BigDecimal.ROUND_UP);
            
            // 考虑合约的最小保证金要求（假设最小保证金为合约大小的1%）
            BigDecimal minMargin = contract.getContractSize().multiply(new BigDecimal("0.01"));
            if (minMargin != null && requiredMargin.compareTo(minMargin) < 0) {
                requiredMargin = minMargin;
            }
            
            log.info("计算开仓所需保证金成功，合约符号: {}, 所需保证金: {}", symbol, requiredMargin);
            return Result.success(requiredMargin);
            
        } catch (Exception e) {
            log.error("计算开仓所需保证金失败", e);
            return Result.error("计算开仓所需保证金失败");
        }
    }
    
    @Override
    public Result<Object> calculateLiquidationPrice(Long userId, String symbol, BigDecimal quantity, BigDecimal price) {
        try {
            log.info("计算强平价格，用户ID: {}, 合约符号: {}, 数量: {}, 价格: {}", userId, symbol, quantity, price);
            
            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            if (quantity == null || quantity.compareTo(BigDecimal.ZERO) == 0) {
                return Result.error("数量不能为0");
            }
            if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("价格必须大于0");
            }
            
            // 获取用户持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectByUserIdAndSymbol(userId, symbol);
            if (positions == null || positions.isEmpty()) {
                return Result.error("持仓不存在");
            }
            FuturesPosition position = positions.get(0); // 取第一个持仓
            
            // 获取合约信息
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取维持保证金率
            BigDecimal maintenanceMarginRate = contract.getMaintenanceMarginRate();
            if (maintenanceMarginRate == null) {
                maintenanceMarginRate = new BigDecimal("0.005"); // 默认0.5%
            }
            
            BigDecimal liquidationPrice;
            BigDecimal positionSize = position.getQuantity();
            BigDecimal entryPrice = position.getAvgPrice();
            BigDecimal margin = position.getMargin();
            
            if (positionSize.compareTo(BigDecimal.ZERO) > 0) {
                // 多头持仓强平价格计算
                // 强平价格 = (保证金 - 名义价值 * 维持保证金率) / 持仓数量
                BigDecimal notionalValue = positionSize.multiply(entryPrice);
                BigDecimal maintenanceMargin = notionalValue.multiply(maintenanceMarginRate);
                liquidationPrice = margin.subtract(maintenanceMargin).divide(positionSize, 8, BigDecimal.ROUND_DOWN);
            } else {
                // 空头持仓强平价格计算
                // 强平价格 = (保证金 + 名义价值 * 维持保证金率) / |持仓数量|
                BigDecimal notionalValue = positionSize.abs().multiply(entryPrice);
                BigDecimal maintenanceMargin = notionalValue.multiply(maintenanceMarginRate);
                liquidationPrice = margin.add(maintenanceMargin).divide(positionSize.abs(), 8, BigDecimal.ROUND_UP);
            }
            
            // 确保强平价格不为负数
            if (liquidationPrice.compareTo(BigDecimal.ZERO) <= 0) {
                liquidationPrice = new BigDecimal("0.01"); // 最小价格
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol);
            result.put("liquidationPrice", liquidationPrice);
            result.put("positionSize", positionSize);
            result.put("entryPrice", entryPrice);
            result.put("margin", margin);
            result.put("maintenanceMarginRate", maintenanceMarginRate);
            
            log.info("计算强平价格成功，用户ID: {}, 合约符号: {}, 强平价格: {}", userId, symbol, liquidationPrice);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("计算强平价格失败", e);
            return Result.error("计算强平价格失败");
        }
    }
    
    @Override
    public Result<Object> getRiskLevel(Long userId, String symbol) {
        try {
            log.info("获取风险等级，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            
            // 获取用户持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectByUserIdAndSymbol(userId, symbol);
            if (positions == null || positions.isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("riskLevel", "NONE");
                result.put("riskRatio", BigDecimal.ZERO);
                result.put("description", "无持仓");
                return Result.success(result);
            }
            FuturesPosition position = positions.get(0); // 取第一个持仓
            
            // 获取合约信息
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 计算保证金率
            BigDecimal marginRatio = calculateMarginRatio(position);
            
            // 获取维持保证金率
            BigDecimal maintenanceMarginRate = contract.getMaintenanceMarginRate();
            if (maintenanceMarginRate == null) {
                maintenanceMarginRate = new BigDecimal("0.005"); // 默认0.5%
            }
            
            // 计算风险比率 = 维持保证金率 / 当前保证金率
            BigDecimal riskRatio = BigDecimal.ZERO;
            if (marginRatio.compareTo(BigDecimal.ZERO) > 0) {
                riskRatio = maintenanceMarginRate.divide(marginRatio, 4, BigDecimal.ROUND_HALF_UP);
            }
            
            // 确定风险等级
            String riskLevel;
            String description;
            
            if (riskRatio.compareTo(new BigDecimal("0.9")) >= 0) {
                riskLevel = "CRITICAL";
                description = "极高风险，即将强平";
            } else if (riskRatio.compareTo(new BigDecimal("0.7")) >= 0) {
                riskLevel = "HIGH";
                description = "高风险，建议增加保证金";
            } else if (riskRatio.compareTo(new BigDecimal("0.5")) >= 0) {
                riskLevel = "MEDIUM";
                description = "中等风险，需要关注";
            } else if (riskRatio.compareTo(new BigDecimal("0.3")) >= 0) {
                riskLevel = "LOW";
                description = "低风险";
            } else {
                riskLevel = "SAFE";
                description = "安全";
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol);
            result.put("riskLevel", riskLevel);
            result.put("riskRatio", riskRatio);
            result.put("marginRatio", marginRatio);
            result.put("maintenanceMarginRate", maintenanceMarginRate);
            result.put("description", description);
            result.put("positionSize", position.getQuantity());
            result.put("margin", position.getMargin());
            result.put("unrealizedPnl", position.getUnrealizedPnl());
            
            log.info("获取风险等级成功，用户ID: {}, 合约符号: {}, 风险等级: {}", userId, symbol, riskLevel);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取风险等级失败", e);
            return Result.error("获取风险等级失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> executeLiquidation(Long userId, String symbol) {
        try {
            log.info("执行强平，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            
            // 获取用户持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectByUserIdAndSymbol(userId, symbol);
            if (positions == null || positions.isEmpty()) {
                return Result.error("无持仓或持仓为0");
            }
            FuturesPosition position = positions.get(0); // 取第一个持仓
            if (position.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                return Result.error("持仓为0");
            }
            
            // 获取合约信息
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取用户保证金账户
            MarginAccount marginAccount = marginAccountMapper.selectByUserId(userId);
            if (marginAccount == null) {
                return Result.error("保证金账户不存在");
            }
            
            ReentrantLock lock = getContractLock(symbol);
            lock.lock();
            
            try {
                // 获取当前标记价格
                BigDecimal markPrice = getMarkPriceInternal(symbol);
                if (markPrice == null) {
                    markPrice = position.getMarkPrice(); // 使用持仓的标记价格
                }
                
                // 计算强平价格和实际盈亏
                BigDecimal positionSize = position.getQuantity();
                BigDecimal entryPrice = position.getAvgPrice();
                BigDecimal margin = position.getMargin();
                
                // 计算已实现盈亏
                BigDecimal realizedPnl;
                if (positionSize.compareTo(BigDecimal.ZERO) > 0) {
                    // 多头持仓：(标记价格 - 开仓价格) * 持仓数量
                    realizedPnl = markPrice.subtract(entryPrice).multiply(positionSize);
                } else {
                    // 空头持仓：(开仓价格 - 标记价格) * |持仓数量|
                    realizedPnl = entryPrice.subtract(markPrice).multiply(positionSize.abs());
                }
                
                // 计算强平费用（通常是名义价值的一定比例）
                BigDecimal notionalValue = positionSize.abs().multiply(markPrice);
                BigDecimal liquidationFee = notionalValue.multiply(new BigDecimal("0.001")); // 0.1%强平费用
                
                // 计算剩余保证金
                BigDecimal remainingMargin = margin.add(realizedPnl).subtract(liquidationFee);
                
                // 创建强平记录
                FuturesLiquidation liquidation = new FuturesLiquidation();
                liquidation.setLiquidationId("LIQ" + System.currentTimeMillis());
                liquidation.setUserId(userId);
                liquidation.setSymbol(symbol);
                liquidation.setSide(positionSize.compareTo(BigDecimal.ZERO) > 0 ? "SELL" : "BUY"); // 强平方向与持仓相反
                liquidation.setQuantity(positionSize.abs());
                liquidation.setPrice(markPrice);
                liquidation.setFee(liquidationFee);
                liquidation.setRealizedPnl(realizedPnl);
                liquidation.setMarginReleased(margin);
                liquidation.setLiquidationType("FORCED");
                liquidation.setCreateTime(LocalDateTime.now());
                
                // 保存强平记录
                futuresLiquidationMapper.insert(liquidation);
                
                // 更新保证金账户
                if (remainingMargin.compareTo(BigDecimal.ZERO) > 0) {
                    // 如果还有剩余保证金，返还给用户
                    marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().add(remainingMargin));
                } else {
                    // 如果保证金不足，记录损失
                    log.warn("强平后保证金不足，用户ID: {}, 合约符号: {}, 损失: {}", userId, symbol, remainingMargin.abs());
                }

                marginAccount.setUpdateTime(LocalDateTime.now());
                marginAccountMapper.updateById(marginAccount);

                // 清空持仓
                position.setQuantity(BigDecimal.ZERO);
                position.setMargin(BigDecimal.ZERO);
                position.setUnrealizedPnl(BigDecimal.ZERO);
                // position.setMarginRatio(BigDecimal.ZERO); // FuturesPosition没有这个方法
                position.setUpdateTime(LocalDateTime.now());
                futuresPositionMapper.updateById(position);
                
                // 取消该用户该合约的所有未成交订单
                List<FuturesOrder> pendingOrders = futuresOrderMapper.selectPendingOrdersByUserAndSymbol(userId, symbol);
                for (FuturesOrder order : pendingOrders) {
                    order.setStatus("CANCELLED");
                    order.setUpdateTime(LocalDateTime.now());
                    futuresOrderMapper.updateById(order);
                    
                    // 释放订单冻结的保证金
                    BigDecimal orderMargin = calculateOrderMargin(order);
                    marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().add(orderMargin));
                }
                
                // 最终更新保证金账户
                marginAccountMapper.updateById(marginAccount);
                
                log.info("强平执行成功，用户ID: {}, 合约符号: {}, 强平价格: {}, 已实现盈亏: {}", 
                        userId, symbol, markPrice, realizedPnl);
                
                return Result.success();
                
            } finally {
                lock.unlock();
            }
            
        } catch (Exception e) {
            log.error("执行强平失败", e);
            return Result.error("执行强平失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> processFundingFee(String symbol) {
        try {
            log.info("处理资金费用，合约符号: {}", symbol);
            
            // 获取当前资金费率
            FuturesFundingRate currentRate = futuresFundingRateMapper.selectCurrentBySymbol(symbol);
            if (currentRate == null) {
                return Result.error("未找到该合约的资金费率信息");
            }
            
            BigDecimal fundingRate = currentRate.getFundingRate();
            BigDecimal markPrice = currentRate.getMarkPrice();
            
            // 获取所有该合约的持仓
            List<FuturesPosition> positions = futuresPositionMapper.selectBySymbol(symbol);
            if (positions.isEmpty()) {
                log.info("合约 {} 无持仓，跳过资金费用处理", symbol);
                return Result.success();
            }
            
            LocalDateTime fundingTime = LocalDateTime.now();
            
            for (FuturesPosition position : positions) {
                if (position.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    continue; // 跳过空持仓
                }

                // 计算资金费用
                // 资金费用 = 持仓名义价值 * 资金费率
                // 名义价值 = 持仓数量 * 标记价格
                BigDecimal notionalValue = position.getQuantity().abs().multiply(markPrice);
                BigDecimal fundingFee = notionalValue.multiply(fundingRate);

                // 多头持仓支付正资金费率，空头持仓收取正资金费率
                if (position.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
                    fundingFee = fundingFee.negate(); // 空头持仓时费用为负（即收取）
                }
                
                // 获取用户保证金账户
                MarginAccount marginAccount = marginAccountMapper.selectByUserId(position.getUserId());
                if (marginAccount == null) {
                    log.error("用户 {} 保证金账户不存在，跳过资金费用处理", position.getUserId());
                    continue;
                }
                
                // 更新保证金账户余额
                marginAccount.setAvailableMargin(marginAccount.getAvailableMargin().subtract(fundingFee));
                marginAccount.setUpdateTime(LocalDateTime.now());
                marginAccountMapper.updateById(marginAccount);

                // 创建资金费用记录
                FuturesFundingFee fundingFeeRecord = new FuturesFundingFee();
                fundingFeeRecord.setUserId(position.getUserId());
                fundingFeeRecord.setSymbol(symbol);
                fundingFeeRecord.setFundingRate(fundingRate);
                fundingFeeRecord.setFundingFee(fundingFee);
                fundingFeeRecord.setPositionSize(position.getQuantity());
                fundingFeeRecord.setMarkPrice(markPrice);
                fundingFeeRecord.setFundingTime(fundingTime);
                fundingFeeRecord.setCreateTime(LocalDateTime.now());
                
                // 保存资金费用记录
                futuresFundingFeeMapper.insert(fundingFeeRecord);
                
                log.debug("处理用户 {} 合约 {} 资金费用: {}", position.getUserId(), symbol, fundingFee);
            }
            
            // 更新资金费率的下次资金时间
            LocalDateTime nextFundingTime = fundingTime.plusHours(8); // 假设每8小时收取一次
            currentRate.setNextFundingTime(nextFundingTime);
            currentRate.setUpdateTime(LocalDateTime.now());
            futuresFundingRateMapper.updateById(currentRate);
            
            log.info("合约 {} 资金费用处理完成，处理持仓数: {}, 资金费率: {}", symbol, positions.size(), fundingRate);
            return Result.success();
            
        } catch (Exception e) {
            log.error("处理资金费用失败", e);
            return Result.error("处理资金费用失败");
        }
    }
    
    @Override
    public Result<Object> getMarketDepth(String symbol, Integer limit) {
        try {
            log.info("获取合约市场深度，合约符号: {}, 限制: {}", symbol, limit);
            
            // 参数验证
            if (limit == null || limit <= 0) {
                limit = 20; // 默认20档
            }
            if (limit > 100) {
                limit = 100; // 最大100档
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取买单深度（按价格降序排列）
            List<FuturesOrder> buyOrders = futuresOrderMapper.selectDepthOrders(symbol, "BUY", limit);
            
            // 获取卖单深度（按价格升序排列）
            List<FuturesOrder> sellOrders = futuresOrderMapper.selectDepthOrders(symbol, "SELL", limit);
            
            // 聚合相同价格的订单
            Map<BigDecimal, BigDecimal> bids = new LinkedHashMap<>(); // 买单
            Map<BigDecimal, BigDecimal> asks = new LinkedHashMap<>(); // 卖单
            
            // 处理买单
            for (FuturesOrder order : buyOrders) {
                BigDecimal price = order.getPrice();
                BigDecimal remainingQuantity = order.getQuantity().subtract(order.getFilledQuantity());
                bids.merge(price, remainingQuantity, BigDecimal::add);
            }
            
            // 处理卖单
            for (FuturesOrder order : sellOrders) {
                BigDecimal price = order.getPrice();
                BigDecimal remainingQuantity = order.getQuantity().subtract(order.getFilledQuantity());
                asks.merge(price, remainingQuantity, BigDecimal::add);
            }
            
            // 转换为数组格式 [价格, 数量]
            List<List<BigDecimal>> bidsList = new ArrayList<>();
            for (Map.Entry<BigDecimal, BigDecimal> entry : bids.entrySet()) {
                List<BigDecimal> bid = Arrays.asList(entry.getKey(), entry.getValue());
                bidsList.add(bid);
            }
            
            List<List<BigDecimal>> asksList = new ArrayList<>();
            for (Map.Entry<BigDecimal, BigDecimal> entry : asks.entrySet()) {
                List<BigDecimal> ask = Arrays.asList(entry.getKey(), entry.getValue());
                asksList.add(ask);
            }
            
            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol);
            result.put("bids", bidsList); // 买单深度
            result.put("asks", asksList); // 卖单深度
            result.put("timestamp", System.currentTimeMillis());
            
            log.info("获取合约市场深度成功，合约符号: {}, 买单档数: {}, 卖单档数: {}", symbol, bidsList.size(), asksList.size());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取合约市场深度失败", e);
            return Result.error("获取合约市场深度失败");
        }
    }
    
    @Override
    public Result<List<Object>> getRecentTrades(String symbol, Integer limit) {
        try {
            log.info("获取合约最新成交，合约符号: {}, 限制: {}", symbol, limit);
            
            // 参数验证
            if (limit == null || limit <= 0) {
                limit = 50; // 默认50条
            }
            if (limit > 500) {
                limit = 500; // 最大500条
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取最新成交记录
            List<FuturesTrade> trades = futuresTradeMapper.selectRecentTrades(symbol, limit);
            
            // 转换为响应格式
            List<Object> result = new ArrayList<>();
            for (FuturesTrade trade : trades) {
                Map<String, Object> tradeInfo = new HashMap<>();
                tradeInfo.put("id", trade.getTradeId());
                tradeInfo.put("price", trade.getPrice());
                tradeInfo.put("quantity", trade.getQuantity());
                tradeInfo.put("side", trade.getSide());
                tradeInfo.put("timestamp", trade.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                tradeInfo.put("isBuyerMaker", "BUY".equals(trade.getSide())); // 买方是否为挂单方
                
                result.add(tradeInfo);
            }
            
            log.info("获取合约最新成交成功，合约符号: {}, 成交记录数: {}", symbol, result.size());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取合约最新成交失败", e);
            return Result.error("获取合约最新成交失败");
        }
    }
    
    @Override
    public Result<Object> get24hrTicker(String symbol) {
        try {
            log.info("获取合约24小时行情，合约符号: {}", symbol);
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 计算24小时前的时间
            LocalDateTime startTime = LocalDateTime.now().minusHours(24);
            
            // 获取24小时内的成交数据
            List<FuturesTrade> trades = futuresTradeMapper.selectTradesByTimeRange(symbol, startTime, LocalDateTime.now());
            
            if (trades.isEmpty()) {
                // 如果没有成交数据，返回基础信息
                Map<String, Object> result = new HashMap<>();
                result.put("symbol", symbol);
                result.put("priceChange", BigDecimal.ZERO);
                result.put("priceChangePercent", BigDecimal.ZERO);
                result.put("weightedAvgPrice", BigDecimal.ZERO);
                result.put("lastPrice", BigDecimal.ZERO);
                result.put("lastQty", BigDecimal.ZERO);
                result.put("openPrice", BigDecimal.ZERO);
                result.put("highPrice", BigDecimal.ZERO);
                result.put("lowPrice", BigDecimal.ZERO);
                result.put("volume", BigDecimal.ZERO);
                result.put("quoteVolume", BigDecimal.ZERO);
                result.put("count", 0);
                result.put("openTime", startTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                result.put("closeTime", System.currentTimeMillis());
                
                return Result.success(result);
            }
            
            // 计算统计数据
            BigDecimal openPrice = trades.get(trades.size() - 1).getPrice(); // 最早的成交价格
            BigDecimal lastPrice = trades.get(0).getPrice(); // 最新的成交价格
            BigDecimal highPrice = BigDecimal.ZERO;
            BigDecimal lowPrice = new BigDecimal("999999999");
            BigDecimal totalVolume = BigDecimal.ZERO;
            BigDecimal totalQuoteVolume = BigDecimal.ZERO;
            
            for (FuturesTrade trade : trades) {
                BigDecimal price = trade.getPrice();
                BigDecimal quantity = trade.getQuantity();
                
                // 更新最高价和最低价
                if (price.compareTo(highPrice) > 0) {
                    highPrice = price;
                }
                if (price.compareTo(lowPrice) < 0) {
                    lowPrice = price;
                }
                
                // 累计成交量和成交额
                totalVolume = totalVolume.add(quantity);
                totalQuoteVolume = totalQuoteVolume.add(price.multiply(quantity));
            }
            
            // 计算价格变化
            BigDecimal priceChange = lastPrice.subtract(openPrice);
            BigDecimal priceChangePercent = BigDecimal.ZERO;
            if (openPrice.compareTo(BigDecimal.ZERO) > 0) {
                priceChangePercent = priceChange.divide(openPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
            }
            
            // 计算加权平均价格
            BigDecimal weightedAvgPrice = BigDecimal.ZERO;
            if (totalVolume.compareTo(BigDecimal.ZERO) > 0) {
                weightedAvgPrice = totalQuoteVolume.divide(totalVolume, 8, BigDecimal.ROUND_HALF_UP);
            }
            
            // 构建响应
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol);
            result.put("priceChange", priceChange);
            result.put("priceChangePercent", priceChangePercent);
            result.put("weightedAvgPrice", weightedAvgPrice);
            result.put("lastPrice", lastPrice);
            result.put("lastQty", trades.get(0).getQuantity());
            result.put("openPrice", openPrice);
            result.put("highPrice", highPrice);
            result.put("lowPrice", lowPrice);
            result.put("volume", totalVolume);
            result.put("quoteVolume", totalQuoteVolume);
            result.put("count", trades.size());
            result.put("openTime", startTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            result.put("closeTime", System.currentTimeMillis());
            
            log.info("获取合约24小时行情成功，合约符号: {}, 成交笔数: {}", symbol, trades.size());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取合约24小时行情失败", e);
            return Result.error("获取合约24小时行情失败");
        }
    }
    
    @Override
    public Result<List<Object>> getKlines(String symbol, String interval, Integer limit) {
        try {
            log.info("获取合约K线数据，合约符号: {}, 间隔: {}, 限制: {}", symbol, interval, limit);
            
            // 参数验证
            if (limit == null || limit <= 0) {
                limit = 100; // 默认100条
            }
            if (limit > 1000) {
                limit = 1000; // 最大1000条
            }
            
            // 验证时间间隔
            if (!isValidInterval(interval)) {
                return Result.error("无效的时间间隔");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取K线数据
            List<FuturesKline> klines = futuresKlineMapper.selectKlines(symbol, interval, limit);
            
            // 转换为响应格式 [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 收盘时间, 成交额, 成交笔数, 主动买入成交量, 主动买入成交额]
            List<Object> result = new ArrayList<>();
            for (FuturesKline kline : klines) {
                List<Object> klineData = Arrays.asList(
                    kline.getOpenTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), // 开盘时间
                    kline.getOpenPrice().toString(), // 开盘价
                    kline.getHighPrice().toString(), // 最高价
                    kline.getLowPrice().toString(), // 最低价
                    kline.getClosePrice().toString(), // 收盘价
                    kline.getVolume().toString(), // 成交量
                    kline.getCloseTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), // 收盘时间
                    kline.getQuoteVolume().toString(), // 成交额
                    kline.getTradeCount(), // 成交笔数
                    kline.getTakerBuyVolume().toString(), // 主动买入成交量
                    kline.getTakerBuyQuoteVolume().toString() // 主动买入成交额
                );
                result.add(klineData);
            }
            
            log.info("获取合约K线数据成功，合约符号: {}, 间隔: {}, K线数量: {}", symbol, interval, result.size());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取合约K线数据失败", e);
            return Result.error("获取合约K线数据失败");
        }
    }
    
    @Override
    public Result<List<Object>> getKlineData(String symbol, String interval, Long startTime, Long endTime, Integer limit) {
        try {
            log.info("获取合约K线数据（详细版本），合约符号: {}, 间隔: {}, 开始时间: {}, 结束时间: {}, 限制: {}", 
                    symbol, interval, startTime, endTime, limit);
            
            // 参数验证
            if (limit == null || limit <= 0) {
                limit = 500; // 默认500条
            }
            if (limit > 1000) {
                limit = 1000; // 最大1000条
            }
            
            // 验证时间间隔
            if (!isValidInterval(interval)) {
                return Result.error("无效的时间间隔");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 时间参数处理
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (startTime != null) {
                startDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault());
            }
            
            if (endTime != null) {
                endDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault());
            }
            
            // 验证时间范围
            if (startDateTime != null && endDateTime != null && startDateTime.isAfter(endDateTime)) {
                return Result.error("开始时间不能晚于结束时间");
            }
            
            // 获取K线数据
            List<FuturesKline> klines = futuresKlineMapper.selectKlinesByTimeRange(
                symbol, interval, startDateTime, endDateTime, limit);
            
            // 转换为响应格式
            List<Object> result = new ArrayList<>();
            for (FuturesKline kline : klines) {
                List<Object> klineData = Arrays.asList(
                    kline.getOpenTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), // 开盘时间
                    kline.getOpenPrice().toString(), // 开盘价
                    kline.getHighPrice().toString(), // 最高价
                    kline.getLowPrice().toString(), // 最低价
                    kline.getClosePrice().toString(), // 收盘价
                    kline.getVolume().toString(), // 成交量
                    kline.getCloseTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), // 收盘时间
                    kline.getQuoteVolume().toString(), // 成交额
                    kline.getTradeCount(), // 成交笔数
                    kline.getTakerBuyVolume().toString(), // 主动买入成交量
                    kline.getTakerBuyQuoteVolume().toString() // 主动买入成交额
                );
                result.add(klineData);
            }
            
            log.info("获取合约K线数据（详细版本）成功，合约符号: {}, 间隔: {}, K线数量: {}", symbol, interval, result.size());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取合约K线数据（详细版本）失败", e);
            return Result.error("获取合约K线数据失败");
        }
    }
    
    @Override
    public Result<BigDecimal> getMarkPrice(String symbol) {
        try {
            log.info("获取标记价格，合约符号: {}", symbol);
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取标记价格（从数据库或缓存中获取）
            BigDecimal markPrice = futuresMarkPriceMapper.getLatestMarkPrice(symbol);
            
            if (markPrice == null) {
                // 如果没有标记价格，使用指数价格作为标记价格
                BigDecimal indexPrice = futuresIndexPriceMapper.getLatestIndexPrice(symbol);
                if (indexPrice != null) {
                    markPrice = indexPrice;
                } else {
                    // 如果指数价格也没有，使用最新成交价格
                    FuturesTrade latestTrade = futuresTradeMapper.getLatestTrade(symbol);
                    if (latestTrade != null) {
                        markPrice = latestTrade.getPrice();
                    } else {
                        return Result.error("无法获取标记价格");
                    }
                }
            }
            
            log.info("获取标记价格成功，合约符号: {}, 标记价格: {}", symbol, markPrice);
            return Result.success(markPrice);
            
        } catch (Exception e) {
            log.error("获取标记价格失败", e);
            return Result.error("获取标记价格失败");
        }
    }
    
    @Override
    public Result<BigDecimal> getIndexPrice(String symbol) {
        try {
            log.info("获取指数价格，合约符号: {}", symbol);
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取指数价格
            BigDecimal indexPrice = futuresIndexPriceMapper.getLatestIndexPrice(symbol);
            
            if (indexPrice == null) {
                // 如果没有指数价格，可以从外部数据源获取或使用最新成交价格
                FuturesTrade latestTrade = futuresTradeMapper.getLatestTrade(symbol);
                if (latestTrade != null) {
                    indexPrice = latestTrade.getPrice();
                } else {
                    return Result.error("无法获取指数价格");
                }
            }
            
            log.info("获取指数价格成功，合约符号: {}, 指数价格: {}", symbol, indexPrice);
            return Result.success(indexPrice);
            
        } catch (Exception e) {
            log.error("获取指数价格失败", e);
            return Result.error("获取指数价格失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> transfer(Long userId, String asset, BigDecimal amount, Integer type) {
        try {
            log.info("资产划转，用户ID: {}, 资产: {}, 金额: {}, 类型: {}", userId, asset, amount, type);
            
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.error("无效的用户ID");
            }
            
            if (!StringUtils.hasText(asset)) {
                return Result.error("资产类型不能为空");
            }
            
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("划转金额必须大于0");
            }
            
            if (type == null || (type != 1 && type != 2)) {
                return Result.error("无效的划转类型"); // 1: 现货到合约, 2: 合约到现货
            }
            
            // 验证用户是否存在
            // TODO: 实现用户验证逻辑
            // if (!userService.userExists(userId)) {
            //     return Result.error("用户不存在");
            // }
            
            if (type == 1) {
                // 现货到合约
                // TODO: 实现现货到合约的划转逻辑
                // 这里需要实现现货账户系统
                log.info("现货到合约划转：用户ID={}, 资产={}, 金额={}", userId, asset, amount);
                // 暂时返回成功，实际实现需要完整的现货账户系统
                
            } else {
                // 合约到现货
                // 检查合约账户余额
                FuturesMarginAccount marginAccount = futuresMarginAccountMapper.findByUserIdAndAsset(userId, asset);
                if (marginAccount == null || marginAccount.getAvailableBalance().compareTo(amount) < 0) {
                    return Result.error("合约账户可用余额不足");
                }
                
                // 扣减合约账户余额
                marginAccount.setBalance(marginAccount.getBalance().subtract(amount));
                marginAccount.setAvailableBalance(marginAccount.getAvailableBalance().subtract(amount));
                marginAccount.setUpdatedAt(LocalDateTime.now());
                futuresMarginAccountMapper.updateById(marginAccount);
                
                // 增加现货账户余额
                BigDecimal spotBalance = spotAccountMapper.getBalance(userId, asset);
                if (spotBalance == null) {
                    spotBalance = BigDecimal.ZERO;
                }
                spotAccountMapper.updateBalance(userId, asset, spotBalance.add(amount));
            }
            
            // 记录划转记录
            AssetTransferRecord transferRecord = new AssetTransferRecord();
            transferRecord.setUserId(userId);
            transferRecord.setAsset(asset);
            transferRecord.setAmount(amount);
            transferRecord.setType(type);
            transferRecord.setStatus(1); // 成功
            transferRecord.setCreatedAt(LocalDateTime.now());
            assetTransferRecordMapper.insert(transferRecord);
            
            log.info("资产划转成功，用户ID: {}, 资产: {}, 金额: {}, 类型: {}", userId, asset, amount, type);
            return Result.success();
            
        } catch (Exception e) {
            log.error("资产划转失败", e);
            return Result.error("资产划转失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> transferAsset(AssetTransferRequest request) {
        try {
            log.info("资产划转，请求参数: {}", request);
            
            // 参数验证
            if (request == null) {
                return Result.error("请求参数不能为空");
            }
            
            if (request.getUserId() == null || request.getUserId() <= 0) {
                return Result.error("无效的用户ID");
            }
            
            if (!StringUtils.hasText(request.getAsset())) {
                return Result.error("资产类型不能为空");
            }
            
            if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("划转金额必须大于0");
            }
            
            if (!StringUtils.hasText(request.getFromAccount()) || !StringUtils.hasText(request.getToAccount())) {
                return Result.error("账户类型不能为空");
            }
            
            // 验证账户类型
            if (!"SPOT".equals(request.getFromAccount()) && !"FUTURES".equals(request.getFromAccount())) {
                return Result.error("无效的源账户类型");
            }
            
            if (!"SPOT".equals(request.getToAccount()) && !"FUTURES".equals(request.getToAccount())) {
                return Result.error("无效的目标账户类型");
            }
            
            if (request.getFromAccount().equals(request.getToAccount())) {
                return Result.error("源账户和目标账户不能相同");
            }
            
            // 验证用户是否存在
            if (!userService.userExists(request.getUserId())) {
                return Result.error("用户不存在");
            }
            
            // 执行划转
            if ("SPOT".equals(request.getFromAccount()) && "FUTURES".equals(request.getToAccount())) {
                // 现货到合约
                return transfer(request.getUserId(), request.getAsset(), request.getAmount(), 1);
            } else if ("FUTURES".equals(request.getFromAccount()) && "SPOT".equals(request.getToAccount())) {
                // 合约到现货
                return transfer(request.getUserId(), request.getAsset(), request.getAmount(), 2);
            } else {
                return Result.error("不支持的划转类型");
            }
            
        } catch (Exception e) {
            log.error("资产划转失败", e);
            return Result.error("资产划转失败");
        }
    }
    
    @Override
    public Result<List<Object>> getBalance(Long userId) {
        try {
            log.info("获取账户余额，用户ID: {}", userId);
            
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.error("无效的用户ID");
            }
            
            // 验证用户是否存在
            if (!userService.userExists(userId)) {
                return Result.error("用户不存在");
            }
            
            // 获取用户所有保证金账户
            List<FuturesMarginAccount> marginAccounts = futuresMarginAccountMapper.findByUserId(userId);
            
            List<Object> balances = new ArrayList<>();
            for (FuturesMarginAccount account : marginAccounts) {
                Map<String, Object> balanceInfo = new HashMap<>();
                balanceInfo.put("asset", account.getAsset());
                balanceInfo.put("balance", account.getBalance().toString());
                balanceInfo.put("availableBalance", account.getAvailableBalance().toString());
                balanceInfo.put("frozenBalance", account.getFrozenBalance().toString());
                balanceInfo.put("updateTime", account.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                balances.add(balanceInfo);
            }
            
            log.info("获取账户余额成功，用户ID: {}, 资产数量: {}", userId, balances.size());
            return Result.success(balances);
            
        } catch (Exception e) {
            log.error("获取账户余额失败", e);
            return Result.error("获取账户余额失败");
        }
    }
    
    @Override
    public Result<List<FuturesBalanceResponse>> getAccountBalance(Long userId) {
        try {
            log.info("获取账户余额（详细版本），用户ID: {}", userId);
            
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.error("无效的用户ID");
            }
            
            // 验证用户是否存在
            if (!userService.userExists(userId)) {
                return Result.error("用户不存在");
            }
            
            // 获取用户所有保证金账户
            List<FuturesMarginAccount> marginAccounts = futuresMarginAccountMapper.findByUserId(userId);
            
            List<FuturesBalanceResponse> balances = new ArrayList<>();
            for (FuturesMarginAccount account : marginAccounts) {
                FuturesBalanceResponse balance = new FuturesBalanceResponse();
                balance.setAsset(account.getAsset());
                balance.setBalance(account.getBalance());
                balance.setAvailableBalance(account.getAvailableBalance());
                balance.setFrozenBalance(account.getFrozenBalance());
                
                // 计算未实现盈亏
                BigDecimal unrealizedPnl = calculateUnrealizedPnl(userId, account.getAsset());
                balance.setUnrealizedPnl(unrealizedPnl);
                
                // 计算保证金率
                BigDecimal marginRatio = calculateMarginRatio(userId, account.getAsset());
                balance.setMarginRatio(marginRatio);
                
                balance.setUpdateTime(account.getUpdatedAt());
                balances.add(balance);
            }
            
            log.info("获取账户余额（详细版本）成功，用户ID: {}, 资产数量: {}", userId, balances.size());
            return Result.success(balances);
            
        } catch (Exception e) {
            log.error("获取账户余额（详细版本）失败", e);
            return Result.error("获取账户余额失败");
        }
    }
    
    @Override
    public Result<List<Object>> getPositionRisk(Long userId, String symbol) {
        try {
            log.info("获取持仓风险，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.error("无效的用户ID");
            }
            
            // 验证用户是否存在
            if (!userService.userExists(userId)) {
                return Result.error("用户不存在");
            }
            
            List<Object> positionRisks = new ArrayList<>();
            
            if (StringUtils.hasText(symbol)) {
                // 获取指定合约的持仓风险
                FuturesPosition position = futuresPositionMapper.findByUserIdAndSymbol(userId, symbol);
                if (position != null && position.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
                    Map<String, Object> riskInfo = buildPositionRiskInfo(position);
                    positionRisks.add(riskInfo);
                }
            } else {
                // 获取所有持仓的风险信息
                List<FuturesPosition> positions = futuresPositionMapper.findByUserId(userId);
                for (FuturesPosition position : positions) {
                    if (position.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
                        Map<String, Object> riskInfo = buildPositionRiskInfo(position);
                        positionRisks.add(riskInfo);
                    }
                }
            }
            
            log.info("获取持仓风险成功，用户ID: {}, 合约符号: {}, 持仓数量: {}", userId, symbol, positionRisks.size());
            return Result.success(positionRisks);
            
        } catch (Exception e) {
            log.error("获取持仓风险失败", e);
            return Result.error("获取持仓风险失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setStopOrder(Long userId, String symbol, BigDecimal stopPrice, BigDecimal takeProfitPrice) {
        try {
            log.info("设置止盈止损，用户ID: {}, 合约符号: {}, 止损价: {}, 止盈价: {}", userId, symbol, stopPrice, takeProfitPrice);
            
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.error("无效的用户ID");
            }
            
            if (!StringUtils.hasText(symbol)) {
                return Result.error("合约符号不能为空");
            }
            
            if (stopPrice == null && takeProfitPrice == null) {
                return Result.error("止损价和止盈价不能同时为空");
            }
            
            if (stopPrice != null && stopPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("止损价必须大于0");
            }
            
            if (takeProfitPrice != null && takeProfitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("止盈价必须大于0");
            }
            
            // 验证用户是否存在
            if (!userService.userExists(userId)) {
                return Result.error("用户不存在");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 检查用户是否有该合约的持仓
            FuturesPosition position = futuresPositionMapper.findByUserIdAndSymbol(userId, symbol);
            if (position == null || position.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                return Result.error("用户没有该合约的持仓");
            }
            
            // 获取当前标记价格
            BigDecimal markPrice = futuresMarkPriceMapper.getLatestMarkPrice(symbol);
            if (markPrice == null) {
                return Result.error("无法获取当前标记价格");
            }
            
            // 验证止损价和止盈价的合理性
            boolean isLong = position.getQuantity().compareTo(BigDecimal.ZERO) > 0;
            
            if (stopPrice != null) {
                if (isLong && stopPrice.compareTo(markPrice) >= 0) {
                    return Result.error("多头持仓的止损价必须低于当前价格");
                }
                if (!isLong && stopPrice.compareTo(markPrice) <= 0) {
                    return Result.error("空头持仓的止损价必须高于当前价格");
                }
            }
            
            if (takeProfitPrice != null) {
                if (isLong && takeProfitPrice.compareTo(markPrice) <= 0) {
                    return Result.error("多头持仓的止盈价必须高于当前价格");
                }
                if (!isLong && takeProfitPrice.compareTo(markPrice) >= 0) {
                    return Result.error("空头持仓的止盈价必须低于当前价格");
                }
            }
            
            // 取消现有的止盈止损订单
            futuresOrderMapper.cancelStopOrdersByUserAndSymbol(userId, symbol);
            
            // 创建止损订单
            if (stopPrice != null) {
                FuturesOrder stopOrder = new FuturesOrder();
                stopOrder.setUserId(userId);
                stopOrder.setSymbol(symbol);
                stopOrder.setOrderType("STOP_MARKET");
                stopOrder.setSide(isLong ? "SELL" : "BUY");
                stopOrder.setQuantity(position.getQuantity().abs());
                stopOrder.setStopPrice(stopPrice);
                stopOrder.setStatus("NEW");
                stopOrder.setTimeInForce("GTC");
                stopOrder.setCreateTime(LocalDateTime.now());
                stopOrder.setUpdateTime(LocalDateTime.now());
                futuresOrderMapper.insert(stopOrder);
            }
            
            // 创建止盈订单
            if (takeProfitPrice != null) {
                FuturesOrder takeProfitOrder = new FuturesOrder();
                takeProfitOrder.setUserId(userId);
                takeProfitOrder.setSymbol(symbol);
                takeProfitOrder.setOrderType("TAKE_PROFIT_MARKET");
                takeProfitOrder.setSide(isLong ? "SELL" : "BUY");
                takeProfitOrder.setQuantity(position.getQuantity().abs());
                takeProfitOrder.setStopPrice(takeProfitPrice);
                takeProfitOrder.setStatus("NEW");
                takeProfitOrder.setTimeInForce("GTC");
                takeProfitOrder.setCreateTime(LocalDateTime.now());
                takeProfitOrder.setUpdateTime(LocalDateTime.now());
                futuresOrderMapper.insert(takeProfitOrder);
            }
            
            log.info("设置止盈止损成功，用户ID: {}, 合约符号: {}, 止损价: {}, 止盈价: {}", userId, symbol, stopPrice, takeProfitPrice);
            return Result.success();
            
        } catch (Exception e) {
            log.error("设置止盈止损失败", e);
            return Result.error("设置止盈止损失败");
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setStopOrder(SetStopOrderRequest request) {
        try {
            log.info("设置止盈止损，请求参数: {}", request);
            
            // 参数验证
            if (request == null) {
                return Result.error("请求参数不能为空");
            }
            
            if (request.getUserId() == null || request.getUserId() <= 0) {
                return Result.error("无效的用户ID");
            }
            
            if (!StringUtils.hasText(request.getSymbol())) {
                return Result.error("合约符号不能为空");
            }
            
            // 调用基础方法
            return setStopOrder(request.getUserId(), request.getSymbol(),
                              request.getStopPrice(), request.getTakeProfitPrice());
            
        } catch (Exception e) {
            log.error("设置止盈止损失败", e);
            return Result.error("设置止盈止损失败");
        }
    }
    
    @Override
    public Result<Object> getTradingStatistics(Long userId, String period) {
        try {
            log.info("获取交易统计，用户ID: {}, 周期: {}", userId, period);
            
            // 参数验证
            if (userId == null || userId <= 0) {
                return Result.error("无效的用户ID");
            }
            
            if (!StringUtils.hasText(period)) {
                period = "24h"; // 默认24小时
            }
            
            // 验证用户是否存在
            if (!userService.userExists(userId)) {
                return Result.error("用户不存在");
            }
            
            // 计算时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime;
            
            switch (period.toLowerCase()) {
                case "1h":
                    startTime = endTime.minusHours(1);
                    break;
                case "24h":
                    startTime = endTime.minusHours(24);
                    break;
                case "7d":
                    startTime = endTime.minusDays(7);
                    break;
                case "30d":
                    startTime = endTime.minusDays(30);
                    break;
                default:
                    return Result.error("不支持的时间周期");
            }
            
            // 获取交易统计数据
            List<FuturesTrade> trades = futuresTradeMapper.findByUserIdAndTimeRange(userId, startTime, endTime);
            
            // 计算统计信息
            Map<String, Object> statistics = new HashMap<>();
            
            BigDecimal totalVolume = BigDecimal.ZERO;
            BigDecimal totalQuoteVolume = BigDecimal.ZERO;
            BigDecimal totalPnl = BigDecimal.ZERO;
            BigDecimal totalFee = BigDecimal.ZERO;
            int totalTrades = trades.size();
            int winTrades = 0;
            int lossTrades = 0;
            
            for (FuturesTrade trade : trades) {
                totalVolume = totalVolume.add(trade.getQuantity());
                totalQuoteVolume = totalQuoteVolume.add(trade.getPrice().multiply(trade.getQuantity()));
                
                // 注意：FuturesTrade实体类可能没有getRealizedPnl()和getFee()方法
                // 这里使用买方和卖方的手续费
                BigDecimal tradeFee = BigDecimal.ZERO;
                if (trade.getBuyerFee() != null) {
                    tradeFee = tradeFee.add(trade.getBuyerFee());
                }
                if (trade.getSellerFee() != null) {
                    tradeFee = tradeFee.add(trade.getSellerFee());
                }
                totalFee = totalFee.add(tradeFee);

                // 暂时跳过盈亏计算，因为FuturesTrade可能没有realizedPnl字段
                // 可以根据实际的实体类字段进行调整
            }
            
            // 计算胜率
            BigDecimal winRate = BigDecimal.ZERO;
            if (totalTrades > 0) {
                winRate = new BigDecimal(winTrades).divide(new BigDecimal(totalTrades), 4, RoundingMode.HALF_UP)
                         .multiply(new BigDecimal("100"));
            }
            
            statistics.put("period", period);
            statistics.put("totalTrades", totalTrades);
            statistics.put("totalVolume", totalVolume.toString());
            statistics.put("totalQuoteVolume", totalQuoteVolume.toString());
            statistics.put("totalPnl", totalPnl.toString());
            statistics.put("totalFee", totalFee.toString());
            statistics.put("winTrades", winTrades);
            statistics.put("lossTrades", lossTrades);
            statistics.put("winRate", winRate.toString() + "%");
            statistics.put("startTime", startTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            statistics.put("endTime", endTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            
            log.info("获取交易统计成功，用户ID: {}, 周期: {}, 交易数量: {}", userId, period, totalTrades);
            return Result.success(statistics);
            
        } catch (Exception e) {
            log.error("获取交易统计失败", e);
            return Result.error("获取交易统计失败");
        }
    }
    
    @Override
    public Result<Object> getInsuranceFund(String symbol) {
        try {
            log.info("获取保险基金余额，合约符号: {}", symbol);
            
            Map<String, Object> insuranceFundInfo = new HashMap<>();
            
            if (StringUtils.hasText(symbol)) {
                // 获取指定合约的保险基金信息
                FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
                if (contract == null) {
                    return Result.error("合约不存在");
                }
                
                // 获取该合约的保险基金余额
                BigDecimal insuranceBalance = futuresInsuranceFundMapper.getBalanceBySymbol(symbol);
                if (insuranceBalance == null) {
                    insuranceBalance = BigDecimal.ZERO;
                }
                
                insuranceFundInfo.put("symbol", symbol);
                insuranceFundInfo.put("balance", insuranceBalance.toString());
                insuranceFundInfo.put("asset", contract.getQuoteAsset());
                
            } else {
                // 获取所有保险基金信息
                List<FuturesInsuranceFund> insuranceFunds = futuresInsuranceFundMapper.getAllInsuranceFunds();
                
                BigDecimal totalBalance = BigDecimal.ZERO;
                List<Map<String, Object>> fundDetails = new ArrayList<>();
                
                for (FuturesInsuranceFund fund : insuranceFunds) {
                    Map<String, Object> fundInfo = new HashMap<>();
                    fundInfo.put("symbol", fund.getSymbol());
                    fundInfo.put("balance", fund.getBalance().toString());
                    fundInfo.put("asset", fund.getAsset());
                    fundInfo.put("updateTime", fund.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                    fundDetails.add(fundInfo);
                    
                    totalBalance = totalBalance.add(fund.getBalance());
                }
                
                insuranceFundInfo.put("totalBalance", totalBalance.toString());
                insuranceFundInfo.put("funds", fundDetails);
            }
            
            insuranceFundInfo.put("updateTime", System.currentTimeMillis());
            
            log.info("获取保险基金余额成功，合约符号: {}", symbol);
            return Result.success(insuranceFundInfo);
            
        } catch (Exception e) {
            log.error("获取保险基金余额失败", e);
            return Result.error("获取保险基金余额失败");
        }
    }
    
    @Override
    public Result<BigDecimal> getInsuranceFundBalance() {
        try {
            log.info("获取保险基金余额（详细版本）");
            
            // 获取所有保险基金余额并计算总和
            List<FuturesInsuranceFund> insuranceFunds = futuresInsuranceFundMapper.getAllInsuranceFunds();
            
            BigDecimal totalBalance = BigDecimal.ZERO;
            for (FuturesInsuranceFund fund : insuranceFunds) {
                if (fund.getBalance() != null) {
                    totalBalance = totalBalance.add(fund.getBalance());
                }
            }
            
            log.info("获取保险基金余额成功，总余额: {}", totalBalance);
            return Result.success(totalBalance);
            
        } catch (Exception e) {
            log.error("获取保险基金余额（详细版本）失败", e);
            return Result.error("获取保险基金余额失败");
        }
    }
    
    @Override
    public Result<Object> getAdlQuantile(Long userId, String symbol) {
        try {
            log.info("获取ADL排队位置，用户ID: {}, 合约符号: {}", userId, symbol);
            
            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            
            if (!StringUtils.hasText(symbol)) {
                return Result.error("合约符号不能为空");
            }
            
            // 验证用户是否存在
            User user = userMapper.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 获取用户在该合约的持仓
            FuturesPosition position = futuresPositionMapper.findByUserIdAndSymbol(userId, symbol);
            if (position == null || position.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                // 没有持仓，ADL排队位置为0
                Map<String, Object> result = new HashMap<>();
                result.put("symbol", symbol);
                result.put("adlQuantile", 0);
                result.put("updateTime", System.currentTimeMillis());
                return Result.success(result);
            }
            
            // 计算ADL排队位置（基于持仓大小、盈亏情况、杠杆等因素）
            // ADL排队位置范围：1-5，数字越大表示越可能被强制平仓
            int adlQuantile = calculateAdlQuantile(position, contract);
            
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol);
            result.put("adlQuantile", adlQuantile);
            result.put("positionAmt", position.getQuantity().toString());
            result.put("unrealizedPnl", position.getUnrealizedPnl().toString());
            result.put("updateTime", System.currentTimeMillis());
            
            log.info("获取ADL排队位置成功，用户ID: {}, 合约符号: {}, ADL位置: {}", userId, symbol, adlQuantile);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取ADL排队位置失败", e);
            return Result.error("获取ADL排队位置失败");
        }
    }
    
    /**
     * 计算ADL排队位置
     * @param position 持仓信息
     * @param contract 合约信息
     * @return ADL排队位置 (1-5)
     */
    private int calculateAdlQuantile(FuturesPosition position, FuturesContract contract) {
        try {
            // 获取当前标记价格
            BigDecimal markPrice = getMarkPriceValue(position.getSymbol());
            if (markPrice == null) {
                return 3; // 默认中等风险
            }
            
            // 计算持仓价值
            BigDecimal positionValue = position.getQuantity().abs().multiply(markPrice);

            // 计算盈亏率
            BigDecimal pnlRate = BigDecimal.ZERO;
            if (position.getAvgPrice().compareTo(BigDecimal.ZERO) > 0) {
                if (position.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    // 多头持仓
                    pnlRate = markPrice.subtract(position.getAvgPrice())
                            .divide(position.getAvgPrice(), 4, RoundingMode.HALF_UP);
                } else {
                    // 空头持仓
                    pnlRate = position.getAvgPrice().subtract(markPrice)
                            .divide(position.getAvgPrice(), 4, RoundingMode.HALF_UP);
                }
            }
            
            // 基于盈亏率和持仓大小计算ADL排队位置
            int quantile = 3; // 默认中等
            
            // 盈利越多，ADL排队位置越高（越容易被平仓）
            if (pnlRate.compareTo(new BigDecimal("0.1")) > 0) {
                quantile = 5; // 高盈利
            } else if (pnlRate.compareTo(new BigDecimal("0.05")) > 0) {
                quantile = 4;
            } else if (pnlRate.compareTo(new BigDecimal("-0.05")) > 0) {
                quantile = 3;
            } else if (pnlRate.compareTo(new BigDecimal("-0.1")) > 0) {
                quantile = 2;
            } else {
                quantile = 1; // 亏损较多
            }
            
            // 持仓越大，风险越高
            BigDecimal largePositionThreshold = new BigDecimal("100000"); // 10万USDT
            if (positionValue.compareTo(largePositionThreshold) > 0) {
                quantile = Math.min(5, quantile + 1);
            }
            
            return quantile;
            
        } catch (Exception e) {
            log.error("计算ADL排队位置失败", e);
            return 3; // 默认中等风险
        }
    }
    
    @Override
    public Result<List<Object>> getContractSpecs(String symbol) {
        try {
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract == null) {
                return Result.error("合约不存在");
            }
            List<Object> result = new ArrayList<>();
            result.add(contract);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取合约规格失败: {}", symbol, e);
            return Result.error("获取合约规格失败");
        }
    }
    
    @Override
    public Result<List<Object>> getLeverageBrackets(String symbol) {
        try {
            log.info("获取杠杆档位，合约符号: {}", symbol);
            
            List<Object> leverageBrackets = new ArrayList<>();
            
            if (StringUtils.hasText(symbol)) {
                // 获取指定合约的杠杆档位
                FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
                if (contract == null) {
                    return Result.error("合约不存在");
                }
                
                List<Map<String, Object>> brackets = createLeverageBrackets(contract);
                leverageBrackets.addAll(brackets);
                
            } else {
                // 获取所有合约的杠杆档位
                List<FuturesContract> contracts = futuresContractMapper.findAllActiveContracts();
                
                for (FuturesContract contract : contracts) {
                    Map<String, Object> contractBrackets = new HashMap<>();
                    contractBrackets.put("symbol", contract.getSymbol());
                    contractBrackets.put("brackets", createLeverageBrackets(contract));
                    leverageBrackets.add(contractBrackets);
                }
            }
            
            log.info("获取杠杆档位成功，合约符号: {}", symbol);
            return Result.success(leverageBrackets);
            
        } catch (Exception e) {
            log.error("获取杠杆档位失败", e);
            return Result.error("获取杠杆档位失败");
        }
    }
    
    /**
     * 创建杠杆档位信息
     * @param contract 合约信息
     * @return 杠杆档位列表
     */
    private List<Map<String, Object>> createLeverageBrackets(FuturesContract contract) {
        List<Map<String, Object>> brackets = new ArrayList<>();
        
        // 根据合约类型设置不同的杠杆档位
        // 这里使用标准的杠杆档位配置
        BigDecimal[][] bracketConfig = {
            // {名义价值上限, 最大杠杆, 维持保证金率, 快速平仓数量}
            {new BigDecimal("5000"), new BigDecimal("125"), new BigDecimal("0.004"), new BigDecimal("0")},
            {new BigDecimal("25000"), new BigDecimal("100"), new BigDecimal("0.005"), new BigDecimal("16.25")},
            {new BigDecimal("100000"), new BigDecimal("50"), new BigDecimal("0.01"), new BigDecimal("41.25")},
            {new BigDecimal("250000"), new BigDecimal("20"), new BigDecimal("0.025"), new BigDecimal("191.25")},
            {new BigDecimal("1000000"), new BigDecimal("10"), new BigDecimal("0.05"), new BigDecimal("816.25")},
            {new BigDecimal("5000000"), new BigDecimal("5"), new BigDecimal("0.1"), new BigDecimal("2316.25")},
            {new BigDecimal("20000000"), new BigDecimal("4"), new BigDecimal("0.125"), new BigDecimal("4816.25")},
            {new BigDecimal("50000000"), new BigDecimal("3"), new BigDecimal("0.15"), new BigDecimal("9316.25")},
            {new BigDecimal("100000000"), new BigDecimal("2"), new BigDecimal("0.25"), new BigDecimal("24316.25")}
        };
        
        for (int i = 0; i < bracketConfig.length; i++) {
            Map<String, Object> bracket = new HashMap<>();
            bracket.put("bracket", i + 1);
            bracket.put("initialLeverage", bracketConfig[i][1].toString());
            bracket.put("notionalCap", bracketConfig[i][0].toString());
            bracket.put("notionalFloor", i == 0 ? "0" : bracketConfig[i-1][0].toString());
            bracket.put("maintMarginRatio", bracketConfig[i][2].toString());
            bracket.put("cum", bracketConfig[i][3].toString());
            brackets.add(bracket);
        }
        
        return brackets;
    }
    
    @Override
    public Result<Object> testOrder(Long userId, FuturesOrderRequest request) {
        try {
            log.info("测试订单，用户ID: {}, 请求参数: {}", userId, request);
            
            // 参数验证
            if (userId == null) {
                return Result.error("用户ID不能为空");
            }
            
            if (request == null) {
                return Result.error("订单请求不能为空");
            }
            
            if (StringUtils.isBlank(request.getSymbol())) {
                return Result.error("合约符号不能为空");
            }
            
            if (!validateOrderRequest(request)) {
                return Result.error("订单参数无效");
            }
            
            // 验证用户是否存在
            User user = userMapper.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 验证合约是否存在
            FuturesContract contract = futuresContractMapper.findBySymbol(request.getSymbol());
            if (contract == null) {
                return Result.error("合约不存在");
            }
            
            // 验证合约状态
            if (!"TRADING".equals(contract.getStatus())) {
                return Result.error("合约当前不可交易");
            }
            
            // 验证订单类型和方向
            if (!Arrays.asList("BUY", "SELL").contains(request.getSide())) {
                return Result.error("订单方向无效");
            }
            
            if (!Arrays.asList("LIMIT", "MARKET", "STOP", "STOP_MARKET", "TAKE_PROFIT", "TAKE_PROFIT_MARKET").contains(request.getOrderType())) {
                return Result.error("订单类型无效");
            }
            
            // 验证价格范围（限价单）
            if ("LIMIT".equals(request.getOrderType()) || "STOP".equals(request.getOrderType()) || "TAKE_PROFIT".equals(request.getOrderType())) {
                BigDecimal minPrice = contract.getMinPrice();
                BigDecimal maxPrice = contract.getMaxPrice();
                
                if (request.getPrice().compareTo(minPrice) < 0 || request.getPrice().compareTo(maxPrice) > 0) {
                    return Result.error("订单价格超出允许范围");
                }
            }
            
            // 验证数量范围
            BigDecimal minQty = contract.getMinQty();
            BigDecimal maxQty = contract.getMaxQty();
            
            if (request.getQuantity().compareTo(minQty) < 0 || request.getQuantity().compareTo(maxQty) > 0) {
                return Result.error("订单数量超出允许范围");
            }
            
            // 验证杠杆倍数
            if (request.getLeverage() > contract.getMaxLeverage()) {
                return Result.error("杠杆倍数超出最大限制");
            }
            
            // 计算所需保证金
            BigDecimal requiredMargin = calculateOrderMargin(request, contract);
            
            // 获取用户保证金账户
            FuturesMarginAccount marginAccount = futuresMarginAccountMapper.findByUserIdAndAsset(userId, contract.getQuoteAsset());
            if (marginAccount == null) {
                return Result.error("用户保证金账户不存在");
            }
            
            // 验证保证金是否充足（测试订单不实际扣除保证金）
            if (marginAccount.getAvailableBalance().compareTo(requiredMargin) < 0) {
                return Result.error("保证金不足");
            }
            
            // 构建测试订单响应
            Map<String, Object> testOrderResult = new HashMap<>();
            testOrderResult.put("symbol", request.getSymbol());
            testOrderResult.put("side", request.getSide());
            testOrderResult.put("orderType", request.getOrderType());
            testOrderResult.put("quantity", request.getQuantity().toString());
            testOrderResult.put("price", request.getPrice().toString());
            testOrderResult.put("leverage", request.getLeverage());
            testOrderResult.put("requiredMargin", requiredMargin.toString());
            testOrderResult.put("availableBalance", marginAccount.getAvailableBalance().toString());
            testOrderResult.put("testResult", "PASS");
            testOrderResult.put("message", "测试订单验证通过，可以正常下单");
            testOrderResult.put("timestamp", System.currentTimeMillis());
            
            log.info("测试订单成功，用户ID: {}, 合约符号: {}", userId, request.getSymbol());
            return Result.success(testOrderResult);
            
        } catch (Exception e) {
            log.error("测试订单失败", e);
            return Result.error("测试订单失败");
        }
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 计算订单所需保证金
     */
    private BigDecimal calculateOrderMargin(FuturesOrderRequest request, FuturesContract contract) {
        // 保证金 = 名义价值 / 杠杆倍数
        // 名义价值 = 数量 * 价格
        BigDecimal notionalValue = request.getQuantity().multiply(request.getPrice());
        return notionalValue.divide(new BigDecimal(request.getLeverage()), 8, BigDecimal.ROUND_UP);
    }
    
    /**
     * 计算订单保证金（基于订单对象）
     */
    private BigDecimal calculateOrderMargin(FuturesOrder order) {
        BigDecimal notionalValue = order.getQuantity().multiply(order.getPrice());
        return notionalValue.divide(new BigDecimal(order.getLeverage()), 8, BigDecimal.ROUND_UP);
    }
    
    /**
     * 生成订单ID
     */
    private String generateOrderId() {
        return "FO" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }
    
    /**
     * 转换订单为响应对象
     */
    private Object convertToOrderResponse(FuturesOrder order) {
        Map<String, Object> response = new HashMap<>();
        response.put("orderId", order.getOrderId());
        response.put("symbol", order.getSymbol());
        response.put("side", order.getSide());
        response.put("orderType", order.getOrderType());
        response.put("quantity", order.getQuantity());
        response.put("price", order.getPrice());
        response.put("leverage", order.getLeverage());
        response.put("status", order.getStatus());
        response.put("createTime", order.getCreateTime());
        response.put("updateTime", order.getUpdateTime());
        return response;
    }
    
    /**
     * 获取合约锁
     */
    private ReentrantLock getContractLock(String symbol) {
        return contractLocks.computeIfAbsent(symbol, k -> new ReentrantLock());
    }

    /**
     * 内部获取标记价格方法
     */
    private BigDecimal getMarkPriceInternal(String symbol) {
        try {
            // 从合约信息中获取标记价格
            FuturesContract contract = futuresContractMapper.findBySymbol(symbol);
            if (contract != null && contract.getMarkPrice() != null) {
                return contract.getMarkPrice();
            }

            // 如果合约没有标记价格，使用指数价格
            if (contract != null && contract.getIndexPrice() != null) {
                return contract.getIndexPrice();
            }

            // 如果都没有，返回null
            return null;
        } catch (Exception e) {
            log.error("获取标记价格失败，合约符号: {}", symbol, e);
            return null;
        }
    }

    /**
     * 获取标记价格值
     */
    private BigDecimal getMarkPriceValue(String symbol) {
        try {
            // 首先尝试从标记价格表获取
            BigDecimal markPrice = futuresMarkPriceMapper.getLatestMarkPrice(symbol);
            if (markPrice != null) {
                return markPrice;
            }

            // 如果没有，从合约信息获取
            return getMarkPriceInternal(symbol);
        } catch (Exception e) {
            log.error("获取标记价格值失败，合约符号: {}", symbol, e);
            return null;
        }
    }
    
    /**
     * 验证订单参数
     */
    private boolean validateOrderRequest(FuturesOrderRequest request) {
        if (request.getQuantity() == null || request.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        if (request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        if (request.getLeverage() == null || request.getLeverage() <= 0 || request.getLeverage() > 100) {
            return false;
        }
        return true;
    }
}