package com.cryptoexchange.service.impl;

import com.cryptoexchange.entity.BackupRecord;
import com.cryptoexchange.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 通知服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    @Autowired(required = false)
    private JavaMailSender mailSender;

    @Override
    public void sendEmailNotification(Long userId, String subject, String content) {
        log.info("发送邮件通知给用户 {}: 主题={}, 内容={}", userId, subject, content);
        
        CompletableFuture.runAsync(() -> {
            try {
                if (mailSender != null) {
                    // 这里需要根据userId获取用户邮箱地址
                    String userEmail = getUserEmail(userId);
                    if (userEmail != null && !userEmail.isEmpty()) {
                        SimpleMailMessage message = new SimpleMailMessage();
                        message.setTo(userEmail);
                        message.setSubject(subject);
                        message.setText(content);
                        message.setFrom("<EMAIL>");
                        
                        mailSender.send(message);
                        log.info("邮件发送成功给用户 {}: {}", userId, userEmail);
                    } else {
                        log.warn("用户 {} 邮箱地址为空，无法发送邮件", userId);
                    }
                } else {
                    log.warn("邮件服务未配置，无法发送邮件给用户 {}", userId);
                }
            } catch (Exception e) {
                log.error("发送邮件失败给用户 {}", userId, e);
            }
        });
    }

    @Override
    public void sendSmsNotification(Long userId, String message) {
        log.info("发送短信通知给用户 {}: 消息={}", userId, message);
        
        CompletableFuture.runAsync(() -> {
            try {
                // 这里需要根据userId获取用户手机号
                String userPhone = getUserPhone(userId);
                if (userPhone != null && !userPhone.isEmpty()) {
                    // 调用短信服务API发送短信
                    boolean success = sendSmsMessage(userPhone, message);
                    if (success) {
                        log.info("短信发送成功给用户 {}: {}", userId, userPhone);
                    } else {
                        log.error("短信发送失败给用户 {}: {}", userId, userPhone);
                    }
                } else {
                    log.warn("用户 {} 手机号为空，无法发送短信", userId);
                }
            } catch (Exception e) {
                log.error("发送短信失败给用户 {}", userId, e);
            }
        });
    }

    @Override
    public void sendInternalMessage(Long userId, String title, String content) {
        log.info("发送站内信给用户 {}: 标题={}, 内容={}", userId, title, content);
        
        try {
            // 创建站内信记录
            createInternalMessage(userId, title, content);
            log.info("站内信创建成功给用户 {}", userId);
        } catch (Exception e) {
            log.error("发送站内信失败给用户 {}", userId, e);
        }
    }

    @Override
    public void sendPushNotification(Long userId, String title, String message, Map<String, Object> data) {
        log.info("发送推送通知给用户 {}: 标题={}, 消息={}, 数据={}", userId, title, message, data);
        
        CompletableFuture.runAsync(() -> {
            try {
                // 获取用户的推送设备令牌
                String deviceToken = getUserDeviceToken(userId);
                if (deviceToken != null && !deviceToken.isEmpty()) {
                    // 调用推送服务发送通知
                    boolean success = sendPushMessage(deviceToken, title, message, data);
                    if (success) {
                        log.info("推送通知发送成功给用户 {}", userId);
                    } else {
                        log.error("推送通知发送失败给用户 {}", userId);
                    }
                } else {
                    log.warn("用户 {} 设备令牌为空，无法发送推送通知", userId);
                }
            } catch (Exception e) {
                log.error("发送推送通知失败给用户 {}", userId, e);
            }
        });
    }

    @Override
    public void sendTradeNotification(Long userId, String symbol, String side, String amount, String price) {
        log.info("发送交易通知给用户 {}: 交易对={}, 方向={}, 数量={}, 价格={}", userId, symbol, side, amount, price);
        
        String title = "交易成功通知";
        String content = String.format("您的%s交易已成功执行：\n交易对：%s\n方向：%s\n数量：%s\n价格：%s\n时间：%s", 
            "买入".equals(side) ? "买入" : "卖出", symbol, side, amount, price, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 发送多种通知
        sendInternalMessage(userId, title, content);
        sendEmailNotification(userId, title, content);
        
        // 发送推送通知
        Map<String, Object> pushData = Map.of(
            "type", "trade",
            "symbol", symbol,
            "side", side,
            "amount", amount,
            "price", price
        );
        sendPushNotification(userId, title, content, pushData);
    }

    @Override
    public void sendOrderStatusNotification(Long userId, Long orderId, String status) {
        log.info("发送订单状态通知给用户 {}: 订单ID={}, 状态={}", userId, orderId, status);
        
        String title = "订单状态更新";
        String content = String.format("您的订单状态已更新：\n订单ID：%d\n状态：%s\n时间：%s", 
            orderId, getStatusDescription(status), 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 发送站内信
        sendInternalMessage(userId, title, content);
        
        // 对于重要状态变更发送推送通知
        if ("FILLED".equals(status) || "CANCELLED".equals(status) || "REJECTED".equals(status)) {
            Map<String, Object> pushData = Map.of(
                "type", "order_status",
                "orderId", orderId,
                "status", status
            );
            sendPushNotification(userId, title, content, pushData);
        }
    }

    @Override
    public void sendDepositNotification(Long userId, String currency, String amount, String status) {
        log.info("发送充值通知给用户 {}: 币种={}, 金额={}, 状态={}", userId, currency, amount, status);
        
        String title = "充值通知";
        String content = String.format("您的充值请求状态更新：\n币种：%s\n金额：%s\n状态：%s\n时间：%s", 
            currency, amount, getDepositStatusDescription(status), 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 发送站内信和邮件通知
        sendInternalMessage(userId, title, content);
        sendEmailNotification(userId, title, content);
        
        // 充值成功时发送推送通知
        if ("SUCCESS".equals(status)) {
            Map<String, Object> pushData = Map.of(
                "type", "deposit",
                "currency", currency,
                "amount", amount,
                "status", status
            );
            sendPushNotification(userId, title, "您的充值已到账", pushData);
        }
    }

    @Override
    public void sendWithdrawNotification(Long userId, String currency, String amount, String status) {
        log.info("发送提现通知给用户 {}: 币种={}, 金额={}, 状态={}", userId, currency, amount, status);
        
        String title = "提现通知";
        String content = String.format("您的提现请求状态更新：\n币种：%s\n金额：%s\n状态：%s\n时间：%s", 
            currency, amount, getWithdrawStatusDescription(status), 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 发送站内信和邮件通知
        sendInternalMessage(userId, title, content);
        sendEmailNotification(userId, title, content);
        
        // 提现状态变更时发送推送通知
        Map<String, Object> pushData = Map.of(
            "type", "withdraw",
            "currency", currency,
            "amount", amount,
            "status", status
        );
        sendPushNotification(userId, title, content, pushData);
    }

    @Override
    public void sendSecurityAlert(Long userId, String alertType, String description) {
        log.info("发送安全警告给用户 {}: 类型={}, 描述={}", userId, alertType, description);
        
        String title = "安全警告";
        String content = String.format("检测到您的账户存在安全风险：\n类型：%s\n描述：%s\n时间：%s\n\n如非本人操作，请立即修改密码并联系客服。", 
            getAlertTypeDescription(alertType), description, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 安全警告需要发送所有类型的通知
        sendInternalMessage(userId, title, content);
        sendEmailNotification(userId, title, content);
        sendSmsNotification(userId, "安全警告：" + description + "，如非本人操作请立即联系客服。");
        
        Map<String, Object> pushData = Map.of(
            "type", "security_alert",
            "alertType", alertType,
            "description", description,
            "priority", "high"
        );
        sendPushNotification(userId, title, "检测到账户安全风险，请立即查看", pushData);
    }

    @Override
    public void sendSystemAnnouncement(String title, String content, String targetUsers) {
        log.info("发送系统公告: 标题={}, 内容={}, 目标用户={}", title, content, targetUsers);
        
        CompletableFuture.runAsync(() -> {
            try {
                // 解析目标用户
                if ("ALL".equals(targetUsers)) {
                    // 发送给所有用户
                    broadcastToAllUsers(title, content);
                } else {
                    // 发送给指定用户组
                    String[] userIds = targetUsers.split(",");
                    for (String userIdStr : userIds) {
                        try {
                            Long userId = Long.parseLong(userIdStr.trim());
                            sendInternalMessage(userId, title, content);
                        } catch (NumberFormatException e) {
                            log.warn("无效的用户ID: {}", userIdStr);
                        }
                    }
                }
                log.info("系统公告发送完成");
            } catch (Exception e) {
                log.error("发送系统公告失败", e);
            }
        });
    }

    @Override
    public void sendLiquidationNotification(Long userId, String symbol, String reason, String amount) {
        log.info("发送强制平仓通知给用户 {}: 交易对={}, 原因={}, 数量={}", userId, symbol, reason, amount);
        
        String title = "强制平仓通知";
        String content = String.format("您的持仓已被强制平仓：\n交易对：%s\n平仓数量：%s\n平仓原因：%s\n时间：%s\n\n请注意风险管理，合理控制仓位。", 
            symbol, amount, reason, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 强制平仓是重要事件，需要发送所有类型的通知
        sendInternalMessage(userId, title, content);
        sendEmailNotification(userId, title, content);
        sendSmsNotification(userId, String.format("您的%s持仓已被强制平仓，数量：%s", symbol, amount));
        
        Map<String, Object> pushData = Map.of(
            "type", "liquidation",
            "symbol", symbol,
            "reason", reason,
            "amount", amount,
            "priority", "high"
        );
        sendPushNotification(userId, title, "您的持仓已被强制平仓", pushData);
    }

    @Override
    public void sendBackupFailureNotification(BackupRecord backupRecord, Exception e) {
        log.error("发送备份失败通知: 备份记录={}, 错误={}", backupRecord, e.getMessage());
        
        String title = "系统备份失败警告";
        String content = String.format("系统备份执行失败：\n备份类型：%s\n失败时间：%s\n错误信息：%s\n\n请立即检查系统状态。", 
            backupRecord != null ? backupRecord.getBackupType() : "未知", 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            e.getMessage());
        
        // 发送给系统管理员
        sendSystemAdminNotification(title, content);
    }

    @Override
    public void sendRecoverySuccessNotification(String backupPath) {
        log.info("发送恢复成功通知: 备份路径={}", backupPath);
        
        String title = "系统恢复成功通知";
        String content = String.format("系统数据恢复成功：\n备份路径：%s\n恢复时间：%s\n\n系统已正常运行。", 
            backupPath, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 发送给系统管理员
        sendSystemAdminNotification(title, content);
    }

    @Override
    public void sendRecoveryFailureNotification(String backupPath, Exception e) {
        log.error("发送恢复失败通知: 备份路径={}, 错误={}", backupPath, e.getMessage());
        
        String title = "系统恢复失败警告";
        String content = String.format("系统数据恢复失败：\n备份路径：%s\n失败时间：%s\n错误信息：%s\n\n请立即检查系统状态并手动恢复。", 
            backupPath, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            e.getMessage());
        
        // 发送给系统管理员
        sendSystemAdminNotification(title, content);
    }

    @Override
    public void sendFailoverSuccessNotification(String targetDataCenter) {
        log.info("发送故障转移成功通知: 目标数据中心={}", targetDataCenter);
        
        String title = "故障转移成功通知";
        String content = String.format("系统故障转移成功：\n目标数据中心：%s\n转移时间：%s\n\n系统已在新数据中心正常运行。", 
            targetDataCenter, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 发送给系统管理员和用户
        sendSystemAdminNotification(title, content);
        sendSystemAnnouncement("系统维护完成", "系统维护已完成，服务已恢复正常。", "ALL");
    }

    @Override
    public void sendFailoverFailureNotification(String targetDataCenter, Exception e) {
        log.error("发送故障转移失败通知: 目标数据中心={}, 错误={}", targetDataCenter, e.getMessage());
        
        String title = "故障转移失败警告";
        String content = String.format("系统故障转移失败：\n目标数据中心：%s\n失败时间：%s\n错误信息：%s\n\n请立即采取应急措施。", 
            targetDataCenter, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            e.getMessage());
        
        // 发送给系统管理员
        sendSystemAdminNotification(title, content);
    }
    
    // 辅助方法
    private String getUserEmail(Long userId) {
        // 这里应该从用户服务或数据库获取用户邮箱
        // 暂时返回模拟邮箱
        return "user" + userId + "@example.com";
    }
    
    private String getUserPhone(Long userId) {
        // 这里应该从用户服务或数据库获取用户手机号
        // 暂时返回模拟手机号
        return "138****" + String.format("%04d", userId % 10000);
    }
    
    private String getUserDeviceToken(Long userId) {
        // 这里应该从用户设备表获取推送令牌
        return "device_token_" + userId;
    }
    
    private boolean sendSmsMessage(String phone, String message) {
        // 这里应该调用实际的短信服务API
        log.info("模拟发送短信到 {}: {}", phone, message);
        return true;
    }
    
    private boolean sendPushMessage(String deviceToken, String title, String message, Map<String, Object> data) {
        // 这里应该调用实际的推送服务API
        log.info("模拟发送推送通知到 {}: 标题={}, 消息={}, 数据={}", deviceToken, title, message, data);
        return true;
    }
    
    private void createInternalMessage(Long userId, String title, String content) {
        // 这里应该将站内信保存到数据库
        log.info("创建站内信记录: 用户={}, 标题={}, 内容={}", userId, title, content);
    }
    
    private void broadcastToAllUsers(String title, String content) {
        // 这里应该获取所有用户并发送站内信
        log.info("广播消息给所有用户: 标题={}, 内容={}", title, content);
    }
    
    private void sendSystemAdminNotification(String title, String content) {
        // 这里应该发送给系统管理员
        log.info("发送系统管理员通知: 标题={}, 内容={}", title, content);
    }
    
    private String getStatusDescription(String status) {
        return switch (status) {
            case "FILLED" -> "已成交";
            case "CANCELLED" -> "已取消";
            case "REJECTED" -> "已拒绝";
            case "PENDING" -> "待处理";
            default -> status;
        };
    }
    
    private String getDepositStatusDescription(String status) {
        return switch (status) {
            case "SUCCESS" -> "充值成功";
            case "PENDING" -> "处理中";
            case "FAILED" -> "充值失败";
            default -> status;
        };
    }
    
    private String getWithdrawStatusDescription(String status) {
        return switch (status) {
            case "SUCCESS" -> "提现成功";
            case "PENDING" -> "处理中";
            case "FAILED" -> "提现失败";
            case "REVIEWING" -> "审核中";
            default -> status;
        };
    }
    
    private String getAlertTypeDescription(String alertType) {
        return switch (alertType) {
            case "LOGIN_ANOMALY" -> "异常登录";
            case "PASSWORD_CHANGE" -> "密码修改";
            case "WITHDRAW_LARGE" -> "大额提现";
            case "API_ABUSE" -> "API滥用";
            default -> alertType;
        };
    }
}