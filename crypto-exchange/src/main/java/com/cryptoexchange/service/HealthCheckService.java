package com.cryptoexchange.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 健康检查服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class HealthCheckService {

    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 检查系统健康状态
     * 
     * @return 健康状态信息
     */
    public Map<String, Object> checkSystemHealth() {
        Map<String, Object> healthStatus = new HashMap<>();
        
        try {
            // 检查数据库连接
            healthStatus.put("database", checkDatabaseHealth());
            
            // 检查Redis连接
            healthStatus.put("redis", checkRedisHealth());
            
            // 检查文件系统
            healthStatus.put("filesystem", checkFileSystemHealth());
            
            // 检查内存使用情况
            healthStatus.put("memory", checkMemoryHealth());
            
            // 检查磁盘空间
            healthStatus.put("disk", checkDiskHealth());
            
            // 整体健康状态
            healthStatus.put("overall", "healthy");
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
            healthStatus.put("overall", "unhealthy");
            healthStatus.put("error", e.getMessage());
        }
        
        return healthStatus;
    }
    
    /**
     * 检查数据库健康状态
     */
    private Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> dbHealth = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try (Connection connection = dataSource.getConnection()) {
            // 执行简单查询测试连接
            try (PreparedStatement stmt = connection.prepareStatement("SELECT 1");
                 ResultSet rs = stmt.executeQuery()) {
                
                if (rs.next() && rs.getInt(1) == 1) {
                    long responseTime = System.currentTimeMillis() - startTime;
                    dbHealth.put("status", "healthy");
                    dbHealth.put("responseTime", responseTime + "ms");
                    dbHealth.put("connectionValid", true);
                } else {
                    dbHealth.put("status", "unhealthy");
                    dbHealth.put("error", "查询结果异常");
                }
            }
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            dbHealth.put("status", "unhealthy");
            dbHealth.put("error", e.getMessage());
            dbHealth.put("connectionValid", false);
        }
        return dbHealth;
    }
    
    /**
     * 检查Redis健康状态
     */
    private Map<String, Object> checkRedisHealth() {
        Map<String, Object> redisHealth = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行ping命令测试连接
            String testKey = "health_check_" + System.currentTimeMillis();
            String testValue = "test";
            
            // 设置测试键值对
            redisTemplate.opsForValue().set(testKey, testValue, 10, TimeUnit.SECONDS);
            
            // 读取测试键值对
            String result = (String) redisTemplate.opsForValue().get(testKey);
            
            if (testValue.equals(result)) {
                long responseTime = System.currentTimeMillis() - startTime;
                redisHealth.put("status", "healthy");
                redisHealth.put("responseTime", responseTime + "ms");
                redisHealth.put("connectionValid", true);
                
                // 清理测试数据
                redisTemplate.delete(testKey);
            } else {
                redisHealth.put("status", "unhealthy");
                redisHealth.put("error", "Redis读写测试失败");
            }
        } catch (Exception e) {
            log.error("Redis健康检查失败", e);
            redisHealth.put("status", "unhealthy");
            redisHealth.put("error", e.getMessage());
            redisHealth.put("connectionValid", false);
        }
        return redisHealth;
    }
    
    /**
     * 检查文件系统健康状态
     */
    private Map<String, Object> checkFileSystemHealth() {
        Map<String, Object> fsHealth = new HashMap<>();
        try {
            File rootDir = new File(System.getProperty("user.dir"));
            long totalSpace = rootDir.getTotalSpace();
            long freeSpace = rootDir.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            double usagePercentage = (double) usedSpace / totalSpace * 100;
            
            // 检查磁盘使用率是否超过80%
            String status = usagePercentage < 80 ? "healthy" : (usagePercentage < 90 ? "warning" : "critical");
            
            fsHealth.put("status", status);
            fsHealth.put("totalSpace", formatBytes(totalSpace));
            fsHealth.put("freeSpace", formatBytes(freeSpace));
            fsHealth.put("usedSpace", formatBytes(usedSpace));
            fsHealth.put("usagePercentage", String.format("%.2f%%", usagePercentage));
            fsHealth.put("availableSpace", usagePercentage < 90 ? "sufficient" : "insufficient");
            
            // 测试文件读写权限
            File testFile = new File(rootDir, "health_check_test.tmp");
            if (testFile.createNewFile()) {
                fsHealth.put("writePermission", true);
                testFile.delete();
            } else {
                fsHealth.put("writePermission", false);
            }
            
        } catch (Exception e) {
            log.error("文件系统健康检查失败", e);
            fsHealth.put("status", "unhealthy");
            fsHealth.put("error", e.getMessage());
        }
        return fsHealth;
    }
    
    /**
     * 检查内存健康状态
     */
    private Map<String, Object> checkMemoryHealth() {
        Map<String, Object> memoryHealth = new HashMap<>();
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double usagePercentage = (double) usedMemory / totalMemory * 100;
            
            memoryHealth.put("status", usagePercentage < 80 ? "healthy" : "warning");
            memoryHealth.put("usagePercentage", String.format("%.2f%%", usagePercentage));
            memoryHealth.put("totalMemory", totalMemory / 1024 / 1024 + "MB");
            memoryHealth.put("usedMemory", usedMemory / 1024 / 1024 + "MB");
        } catch (Exception e) {
            memoryHealth.put("status", "unhealthy");
            memoryHealth.put("error", e.getMessage());
        }
        return memoryHealth;
    }
    
    /**
     * 检查磁盘健康状态
     */
    private Map<String, Object> checkDiskHealth() {
        Map<String, Object> diskHealth = new HashMap<>();
        try {
            File[] roots = File.listRoots();
            Map<String, Object> diskInfo = new HashMap<>();
            
            boolean allHealthy = true;
            for (File root : roots) {
                Map<String, Object> rootInfo = new HashMap<>();
                long totalSpace = root.getTotalSpace();
                long freeSpace = root.getFreeSpace();
                long usedSpace = totalSpace - freeSpace;
                double usagePercentage = totalSpace > 0 ? (double) usedSpace / totalSpace * 100 : 0;
                
                String rootStatus = usagePercentage < 85 ? "healthy" : (usagePercentage < 95 ? "warning" : "critical");
                if (!"healthy".equals(rootStatus)) {
                    allHealthy = false;
                }
                
                rootInfo.put("status", rootStatus);
                rootInfo.put("totalSpace", formatBytes(totalSpace));
                rootInfo.put("freeSpace", formatBytes(freeSpace));
                rootInfo.put("usedSpace", formatBytes(usedSpace));
                rootInfo.put("usagePercentage", String.format("%.2f%%", usagePercentage));
                
                diskInfo.put(root.getPath(), rootInfo);
            }
            
            diskHealth.put("status", allHealthy ? "healthy" : "warning");
            diskHealth.put("disks", diskInfo);
            diskHealth.put("availableSpace", allHealthy ? "sufficient" : "limited");
            
        } catch (Exception e) {
            log.error("磁盘健康检查失败", e);
            diskHealth.put("status", "unhealthy");
            diskHealth.put("error", e.getMessage());
        }
        return diskHealth;
    }

    /**
     * 执行完整健康检查
     *
     * @return 是否健康
     */
    public boolean performFullHealthCheck() {
        try {
            Map<String, Object> healthStatus = checkSystemHealth();
            String overallStatus = (String) healthStatus.get("overall");
            return "healthy".equals(overallStatus);
        } catch (Exception e) {
            log.error("完整健康检查失败", e);
            return false;
        }
    }

    /**
     * 检查数据中心健康状态
     *
     * @param dataCenter 数据中心名称
     * @return 是否健康
     */
    public boolean checkDataCenterHealth(String dataCenter) {
        try {
            log.info("检查数据中心健康状态: {}", dataCenter);
            
            // 检查数据中心的各项指标
            Map<String, Object> healthStatus = checkSystemHealth();
            String overallStatus = (String) healthStatus.get("overall");
            
            // 检查数据库连接
            Map<String, Object> dbHealth = (Map<String, Object>) healthStatus.get("database");
            boolean dbHealthy = "healthy".equals(dbHealth.get("status"));
            
            // 检查Redis连接
            Map<String, Object> redisHealth = (Map<String, Object>) healthStatus.get("redis");
            boolean redisHealthy = "healthy".equals(redisHealth.get("status"));
            
            // 检查文件系统
            Map<String, Object> fsHealth = (Map<String, Object>) healthStatus.get("filesystem");
            boolean fsHealthy = "healthy".equals(fsHealth.get("status"));
            
            // 检查内存使用
            Map<String, Object> memHealth = (Map<String, Object>) healthStatus.get("memory");
            boolean memHealthy = "healthy".equals(memHealth.get("status"));
            
            boolean isHealthy = "healthy".equals(overallStatus) && dbHealthy && redisHealthy && fsHealthy && memHealthy;
            
            log.info("数据中心 {} 健康状态: {}", dataCenter, isHealthy ? "健康" : "异常");
            return isHealthy;
            
        } catch (Exception e) {
            log.error("检查数据中心健康状态失败: {}", dataCenter, e);
            return false;
        }
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}