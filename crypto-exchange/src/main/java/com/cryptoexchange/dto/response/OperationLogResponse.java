package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "操作日志响应")
public class OperationLogResponse {

    @Schema(description = "ID", example = "123456")
    private Long id;

    @Schema(description = "日志ID", example = "123456")
    private Long logId;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    @Schema(description = "操作类型", example = "LOGIN")
    private String operationType;

    @Schema(description = "操作描述", example = "用户登录")
    private String operationDescription;

    @Schema(description = "操作结果", example = "SUCCESS")
    private String operationResult;

    @Schema(description = "IP地址", example = "*************")
    private String ipAddress;

    @Schema(description = "地理位置", example = "北京市朝阳区")
    private String location;

    @Schema(description = "设备信息", example = "Chrome/Windows")
    private String deviceInfo;

    @Schema(description = "风险等级", example = "LOW")
    private String riskLevel;

    @Schema(description = "操作详情")
    private String operationDetails;

    @Schema(description = "关联订单号")
    private String relatedOrderId;

    public OperationLogResponse() {}

    public OperationLogResponse(Long logId, LocalDateTime operationTime, String operationType, 
                              String operationDescription, String operationResult, String ipAddress) {
        this.logId = logId;
        this.operationTime = operationTime;
        this.operationType = operationType;
        this.operationDescription = operationDescription;
        this.operationResult = operationResult;
        this.ipAddress = ipAddress;
    }
}