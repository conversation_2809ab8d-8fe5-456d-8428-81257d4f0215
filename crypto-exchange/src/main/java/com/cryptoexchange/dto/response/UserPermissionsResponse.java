package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 用户权限响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户权限响应")
public class UserPermissionsResponse {

    @Schema(description = "用户角色列表")
    private List<String> roles;

    @Schema(description = "权限列表")
    private List<String> permissions;

    @Schema(description = "功能权限")
    private FunctionPermissions functionPermissions;

    @Schema(description = "交易权限")
    private TradingPermissions tradingPermissions;

    @Schema(description = "资产权限")
    private AssetPermissions assetPermissions;

    @Schema(description = "API权限")
    private ApiPermissions apiPermissions;

    @Schema(description = "地域限制")
    private List<String> allowedRegions;

    @Schema(description = "IP白名单")
    private List<String> allowedIpAddresses;

    @Schema(description = "权限限制")
    private Map<String, Object> restrictions;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "用户等级")
    private Integer userLevel;
    
    @Schema(description = "是否VIP用户")
    private Boolean isVip;
    
    @Schema(description = "每日交易限额")
    private java.math.BigDecimal dailyTradeLimit;
    
    @Schema(description = "每日提现限额")
    private java.math.BigDecimal dailyWithdrawLimit;
    
    @Schema(description = "是否可以杠杆交易")
    private Boolean canMarginTrade;
    
    @Schema(description = "是否可以合约交易")
    private Boolean canFuturesTrade;

    @Data
    @Schema(description = "功能权限")
    public static class FunctionPermissions {
        @Schema(description = "是否可以登录", example = "true")
        private Boolean canLogin;
        
        @Schema(description = "是否可以交易", example = "true")
        private Boolean canTrade;
        
        @Schema(description = "是否可以充值", example = "true")
        private Boolean canDeposit;
        
        @Schema(description = "是否可以提现", example = "true")
        private Boolean canWithdraw;
        
        @Schema(description = "是否可以转账", example = "false")
        private Boolean canTransfer;
        
        @Schema(description = "是否可以使用API", example = "true")
        private Boolean canUseApi;
        
        @Schema(description = "是否可以参与活动", example = "true")
        private Boolean canParticipateEvents;
    }

    @Data
    @Schema(description = "交易权限")
    public static class TradingPermissions {
        @Schema(description = "是否可以现货交易", example = "true")
        private Boolean canSpotTrade;
        
        @Schema(description = "是否可以合约交易", example = "false")
        private Boolean canFuturesTrade;
        
        @Schema(description = "是否可以杠杆交易", example = "false")
        private Boolean canMarginTrade;
        
        @Schema(description = "是否可以期权交易", example = "false")
        private Boolean canOptionsTrade;
        
        @Schema(description = "允许的交易对")
        private List<String> allowedTradingPairs;
        
        @Schema(description = "禁止的交易对")
        private List<String> restrictedTradingPairs;
    }

    @Data
    @Schema(description = "资产权限")
    public static class AssetPermissions {
        @Schema(description = "允许的币种")
        private List<String> allowedCurrencies;
        
        @Schema(description = "禁止的币种")
        private List<String> restrictedCurrencies;
        
        @Schema(description = "是否可以查看资产", example = "true")
        private Boolean canViewAssets;
        
        @Schema(description = "是否可以查看交易历史", example = "true")
        private Boolean canViewTradeHistory;
        
        @Schema(description = "是否可以导出数据", example = "false")
        private Boolean canExportData;
    }

    @Data
    @Schema(description = "API权限")
    public static class ApiPermissions {
        @Schema(description = "是否可以创建API Key", example = "true")
        private Boolean canCreateApiKey;
        
        @Schema(description = "是否可以读取数据", example = "true")
        private Boolean canRead;
        
        @Schema(description = "是否可以交易", example = "false")
        private Boolean canTrade;
        
        @Schema(description = "是否可以提现", example = "false")
        private Boolean canWithdraw;
        
        @Schema(description = "API调用频率限制")
        private Integer rateLimit;
        
        @Schema(description = "允许的API端点")
        private List<String> allowedEndpoints;
    }

    public UserPermissionsResponse() {}

    public UserPermissionsResponse(List<String> roles, List<String> permissions) {
        this.roles = roles;
        this.permissions = permissions;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }
    
    public void setIsVip(Boolean isVip) {
        this.isVip = isVip;
    }
    
    public void setDailyTradeLimit(java.math.BigDecimal dailyTradeLimit) {
        this.dailyTradeLimit = dailyTradeLimit;
    }
    
    public void setDailyWithdrawLimit(java.math.BigDecimal dailyWithdrawLimit) {
        this.dailyWithdrawLimit = dailyWithdrawLimit;
    }
    
    public void setCanMarginTrade(Boolean canMarginTrade) {
        this.canMarginTrade = canMarginTrade;
    }
    
    public void setCanFuturesTrade(Boolean canFuturesTrade) {
        this.canFuturesTrade = canFuturesTrade;
    }
}