package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 推荐信息响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "推荐信息响应")
public class ReferralInfoResponse {

    @Schema(description = "推荐码", example = "ABC123456")
    private String referralCode;

    @Schema(description = "推荐链接", example = "https://exchange.com/register?ref=ABC123456")
    private String referralLink;

    @Schema(description = "二维码URL", example = "https://exchange.com/qr/ABC123456.png")
    private String qrCodeUrl;

    @Schema(description = "总推荐人数", example = "25")
    private Integer totalReferrals;

    @Schema(description = "有效推荐人数", example = "20")
    private Integer activeReferrals;

    @Schema(description = "累计推荐奖励")
    private BigDecimal totalRewards;

    @Schema(description = "本月推荐奖励")
    private BigDecimal monthlyRewards;

    @Schema(description = "推荐奖励比例", example = "0.2")
    private BigDecimal rewardRate;

    @Schema(description = "推荐等级", example = "1")
    private Integer referralLevel;

    @Schema(description = "推荐等级名称", example = "初级推荐员")
    private String referralLevelName;

    @Schema(description = "下级推荐人数", example = "5")
    private Integer subReferrals;

    @Schema(description = "推荐统计信息")
    private ReferralStats stats;

    @Schema(description = "最近推荐记录")
    private List<RecentReferral> recentReferrals;

    @Data
    @Schema(description = "推荐统计信息")
    public static class ReferralStats {
        @Schema(description = "今日新增推荐", example = "2")
        private Integer todayReferrals;
        
        @Schema(description = "本周新增推荐", example = "8")
        private Integer weeklyReferrals;
        
        @Schema(description = "本月新增推荐", example = "15")
        private Integer monthlyReferrals;
        
        @Schema(description = "今日奖励")
        private BigDecimal todayRewards;
        
        @Schema(description = "本周奖励")
        private BigDecimal weeklyRewards;
    }

    @Data
    @Schema(description = "最近推荐记录")
    public static class RecentReferral {
        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "用户名", example = "user123")
        private String username;

        @Schema(description = "用户昵称", example = "张***")
        private String nickname;
        
        @Schema(description = "注册时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime registerTime;

        @Schema(description = "注册时间（别名）")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime registrationTime;

        @Schema(description = "是否已激活", example = "true")
        private Boolean isActive;

        @Schema(description = "累计交易量")
        private BigDecimal totalTradeVolume;

        @Schema(description = "推荐奖励")
        private BigDecimal rewards;

        @Schema(description = "佣金收入")
        private BigDecimal commissionEarned;
    }

    public ReferralInfoResponse() {}

    public ReferralInfoResponse(String referralCode, String referralLink, Integer totalReferrals, 
                              Integer activeReferrals, BigDecimal totalRewards) {
        this.referralCode = referralCode;
        this.referralLink = referralLink;
        this.totalReferrals = totalReferrals;
        this.activeReferrals = activeReferrals;
        this.totalRewards = totalRewards;
    }
}