package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户等级响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户等级响应")
public class UserLevelResponse {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "当前等级", example = "VIP1")
    private String currentLevel;

    @Schema(description = "当前等级名称", example = "VIP用户")
    private String currentLevelName;

    @Schema(description = "等级名称", example = "白银会员")
    private String levelName;

    @Schema(description = "等级描述", example = "享受更低的交易手续费")
    private String levelDescription;

    @Schema(description = "当前积分", example = "1500")
    private Integer currentPoints;

    @Schema(description = "当前等级积分", example = "1500")
    private Integer currentLevelScore;

    @Schema(description = "升级所需积分", example = "2000")
    private Integer nextLevelPoints;

    @Schema(description = "下一等级积分", example = "2000")
    private Integer nextLevelScore;

    @Schema(description = "进度百分比", example = "75.0")
    private BigDecimal progressPercentage;

    @Schema(description = "升级进度百分比", example = "75.0")
    private BigDecimal upgradeProgress;

    @Schema(description = "交易手续费率", example = "0.001")
    private BigDecimal tradingFeeRate;

    @Schema(description = "提现手续费率", example = "0.0005")
    private BigDecimal withdrawalFeeRate;

    @Schema(description = "每日提现限额")
    private BigDecimal dailyWithdrawalLimit;

    @Schema(description = "单笔提现限额")
    private BigDecimal singleWithdrawalLimit;

    @Schema(description = "等级特权列表")
    private List<String> privileges;

    @Schema(description = "当前等级权益")
    private List<String> currentLevelBenefits;

    @Schema(description = "下一等级权益")
    private List<String> nextLevelBenefits;

    @Schema(description = "下一等级名称")
    private String nextLevelName;

    @Schema(description = "下一等级信息")
    private NextLevelInfo nextLevel;

    @Data
    @Schema(description = "下一等级信息")
    public static class NextLevelInfo {
        @Schema(description = "等级代码", example = "VIP2")
        private String levelCode;
        
        @Schema(description = "等级名称", example = "黄金会员")
        private String levelName;
        
        @Schema(description = "所需积分", example = "2000")
        private Integer requiredPoints;
        
        @Schema(description = "交易手续费率", example = "0.0008")
        private BigDecimal tradingFeeRate;
        
        @Schema(description = "特权列表")
        private List<String> privileges;
    }

    public UserLevelResponse() {}

    public UserLevelResponse(String currentLevel, String levelName, Integer currentPoints, 
                           Integer nextLevelPoints, BigDecimal tradingFeeRate) {
        this.currentLevel = currentLevel;
        this.levelName = levelName;
        this.currentPoints = currentPoints;
        this.nextLevelPoints = nextLevelPoints;
        this.tradingFeeRate = tradingFeeRate;
        
        if (nextLevelPoints != null && nextLevelPoints > 0) {
            this.upgradeProgress = BigDecimal.valueOf(currentPoints)
                .divide(BigDecimal.valueOf(nextLevelPoints), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }
    }
}