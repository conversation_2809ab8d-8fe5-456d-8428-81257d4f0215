package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 签到记录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "签到记录响应")
public class CheckInRecordResponse {

    @Schema(description = "记录ID", example = "123456")
    private Long recordId;

    @Schema(description = "签到日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    @Schema(description = "签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkInTime;

    @Schema(description = "连续签到天数", example = "5")
    private Integer consecutiveDays;

    @Schema(description = "连续天数（别名）", example = "5")
    private Integer continuousDays;

    @Schema(description = "奖励类型", example = "POINTS")
    private String rewardType;

    @Schema(description = "奖励名称", example = "积分")
    private String rewardName;

    @Schema(description = "奖励数量")
    private BigDecimal rewardAmount;

    @Schema(description = "奖励币种", example = "USDT")
    private String rewardCurrency;

    @Schema(description = "奖励描述", example = "连续签到5天获得50积分")
    private String rewardDescription;

    @Schema(description = "签到状态", example = "SUCCESS")
    private String status;

    @Schema(description = "IP地址", example = "*************")
    private String ipAddress;

    @Schema(description = "设备信息", example = "Chrome/Windows")
    private String deviceInfo;

    @Schema(description = "设备（别名）", example = "Chrome/Windows")
    private String device;

    @Schema(description = "是否为补签", example = "false")
    private Boolean isMakeUp;

    @Schema(description = "是否为补充签到", example = "false")
    private Boolean isSupplementary;

    @Schema(description = "备注信息")
    private String remarks;

    @Schema(description = "备注（别名）")
    private String remark;

    public CheckInRecordResponse() {}

    public CheckInRecordResponse(Long recordId, LocalDate checkInDate, LocalDateTime checkInTime, 
                               Integer consecutiveDays, String rewardType, BigDecimal rewardAmount) {
        this.recordId = recordId;
        this.checkInDate = checkInDate;
        this.checkInTime = checkInTime;
        this.consecutiveDays = consecutiveDays;
        this.rewardType = rewardType;
        this.rewardAmount = rewardAmount;
        this.status = "SUCCESS";
        this.isMakeUp = false;
    }
}