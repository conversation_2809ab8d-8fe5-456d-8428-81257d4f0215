package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * KYC状态响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "KYC状态响应")
public class KycStatusResponse {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "KYC状态码", example = "1")
    private Integer kycStatus;

    @Schema(description = "KYC状态名称", example = "审核中")
    private String kycStatusName;

    @Schema(description = "KYC状态", example = "PENDING")
    private String status;

    @Schema(description = "状态描述", example = "审核中")
    private String statusDescription;

    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Schema(description = "身份证号（脱敏）", example = "110101********1234")
    private String maskedIdNumber;

    @Schema(description = "申请类型", example = "INDIVIDUAL")
    private String applicationType;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationTime;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    @Schema(description = "审核员ID")
    private Long reviewerId;

    @Schema(description = "审核员姓名")
    private String reviewerName;

    @Schema(description = "拒绝原因")
    private String rejectReason;

    @Schema(description = "审核员备注")
    private String reviewerNote;

    @Schema(description = "KYC等级", example = "1")
    private Integer kycLevel;

    @Schema(description = "是否可以重新申请", example = "true")
    private Boolean canReapply;

    @Schema(description = "KYC文档列表")
    private List<KycDocument> documents;

    @Schema(description = "下一步操作提示")
    private String nextStepTip;

    public KycStatusResponse() {}

    public KycStatusResponse(String status, String statusDescription, String realName,
                           String maskedIdNumber, String applicationType) {
        this.status = status;
        this.statusDescription = statusDescription;
        this.realName = realName;
        this.maskedIdNumber = maskedIdNumber;
        this.applicationType = applicationType;
    }

    @Data
    @Schema(description = "KYC文档信息")
    public static class KycDocument {
        @Schema(description = "文档类型")
        private String documentType;

        @Schema(description = "文档类型名称")
        private String documentTypeName;

        @Schema(description = "文档状态")
        private String status;

        @Schema(description = "上传时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime uploadTime;

        @Schema(description = "文档URL")
        private String documentUrl;

        @Schema(description = "审核备注")
        private String reviewNote;
    }
}