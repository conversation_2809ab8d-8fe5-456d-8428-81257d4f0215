package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推荐用户响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "推荐用户响应")
public class ReferralUserResponse {

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户名", example = "user***123")
    private String username;

    @Schema(description = "昵称", example = "张***")
    private String nickname;

    @Schema(description = "邮箱（脱敏）", example = "user***@example.com")
    private String maskedEmail;

    @Schema(description = "手机号（脱敏）", example = "138****5678")
    private String maskedPhone;

    @Schema(description = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    @Schema(description = "首次交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstTradeTime;

    @Schema(description = "最后活跃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActiveTime;

    @Schema(description = "用户状态", example = "ACTIVE")
    private String userStatus;

    @Schema(description = "KYC状态", example = "VERIFIED")
    private String kycStatus;

    @Schema(description = "用户等级", example = "VIP1")
    private String userLevel;

    @Schema(description = "累计交易量")
    private BigDecimal totalTradeVolume;

    @Schema(description = "累计交易次数", example = "150")
    private Integer totalTradeCount;

    @Schema(description = "推荐奖励")
    private BigDecimal referralReward;

    @Schema(description = "本月交易量")
    private BigDecimal monthlyTradeVolume;

    @Schema(description = "是否活跃用户", example = "true")
    private Boolean isActive;

    @Schema(description = "推荐层级", example = "1")
    private Integer referralLevel;

    @Schema(description = "下级推荐人数", example = "3")
    private Integer subReferralCount;
    
    @Schema(description = "总推荐人数")
    private Long totalReferrals;
    
    @Schema(description = "活跃推荐人数")
    private Long activeReferrals;
    
    @Schema(description = "总佣金")
    private BigDecimal totalCommission;
    
    @Schema(description = "本月佣金")
    private BigDecimal thisMonthCommission;
    
    @Schema(description = "推荐人ID")
    private Long referrerId;
    
    @Schema(description = "推荐用户列表")
    private java.util.List<ReferralUser> referralUsers;

    public ReferralUserResponse() {}

    public ReferralUserResponse(Long userId, String username, String nickname, 
                              LocalDateTime registerTime, String userStatus, 
                              BigDecimal totalTradeVolume, BigDecimal referralReward) {
        this.userId = userId;
        this.username = username;
        this.nickname = nickname;
        this.registerTime = registerTime;
        this.userStatus = userStatus;
        this.totalTradeVolume = totalTradeVolume;
        this.referralReward = referralReward;
    }
    
    /**
     * 推荐用户内部类
     */
    @Data
    @Schema(description = "推荐用户信息")
    public static class ReferralUser {
        
        @Schema(description = "用户ID")
        private Long userId;
        
        @Schema(description = "用户名")
        private String username;
        
        @Schema(description = "昵称")
        private String nickname;
        
        @Schema(description = "邮箱（脱敏）")
        private String maskedEmail;
        
        @Schema(description = "手机号（脱敏）")
        private String maskedPhone;
        
        @Schema(description = "注册时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime registerTime;
        
        @Schema(description = "首次交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime firstTradeTime;
        
        @Schema(description = "最后活跃时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastActiveTime;
        
        @Schema(description = "用户状态")
        private String userStatus;
        
        @Schema(description = "KYC状态")
        private String kycStatus;
        
        @Schema(description = "用户等级")
        private String userLevel;
        
        @Schema(description = "累计交易量")
        private BigDecimal totalTradeVolume;
        
        @Schema(description = "累计交易次数")
        private Integer totalTradeCount;
        
        @Schema(description = "推荐奖励")
        private BigDecimal referralReward;
        
        @Schema(description = "本月交易量")
        private BigDecimal monthlyTradeVolume;
        
        @Schema(description = "是否活跃用户")
        private Boolean isActive;
        
        @Schema(description = "推荐层级")
        private Integer referralLevel;
        
        @Schema(description = "下级推荐人数")
        private Integer subReferralCount;
    }
    
    public void setTotalReferrals(Long totalReferrals) {
        this.totalReferrals = totalReferrals;
    }
    
    public void setActiveReferrals(Long activeReferrals) {
        this.activeReferrals = activeReferrals;
    }
    
    public void setTotalCommission(BigDecimal totalCommission) {
        this.totalCommission = totalCommission;
    }
    
    public void setThisMonthCommission(BigDecimal thisMonthCommission) {
        this.thisMonthCommission = thisMonthCommission;
    }
}