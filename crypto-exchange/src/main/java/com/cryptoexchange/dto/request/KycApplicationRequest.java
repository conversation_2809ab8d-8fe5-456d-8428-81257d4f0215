package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * KYC申请请求
 */
public class KycApplicationRequest {
    
    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String realName;
    
    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idNumber;
    
    /**
     * 身份证正面照片URL
     */
    @NotBlank(message = "身份证正面照片不能为空")
    private String idCardFrontUrl;
    
    /**
     * 身份证反面照片URL
     */
    @NotBlank(message = "身份证反面照片不能为空")
    private String idCardBackUrl;
    
    /**
     * 手持身份证照片URL
     */
    @NotBlank(message = "手持身份证照片不能为空")
    private String idCardHandUrl;
    
    /**
     * 国籍
     */
    private String nationality;
    
    /**
     * 职业
     */
    private String occupation;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 申请类型：INDIVIDUAL-个人认证，ENTERPRISE-企业认证
     */
    private String applicationType;
    
    /**
     * 收入来源
     */
    private String incomeSource;
    
    /**
     * 全名
     */
    private String fullName;
    
    /**
     * 证件类型
     */
    private String idType;
    
    /**
     * 出生日期
     */
    private String dateOfBirth;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 国家
     */
    private String country;
    
    /**
     * 邮政编码
     */
    private String postalCode;
    
    /**
     * 身份证正面图片
     */
    private String idFrontImage;
    
    /**
     * 身份证背面图片
     */
    private String idBackImage;
    
    /**
     * 自拍照片
     */
    private String selfieImage;
    
    /**
     * 地址证明图片
     */
    private String proofOfAddressImage;
    
    // Getters and Setters
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getIdNumber() {
        return idNumber;
    }
    
    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    
    public String getIdCardFrontUrl() {
        return idCardFrontUrl;
    }
    
    public void setIdCardFrontUrl(String idCardFrontUrl) {
        this.idCardFrontUrl = idCardFrontUrl;
    }
    
    public String getIdCardBackUrl() {
        return idCardBackUrl;
    }
    
    public void setIdCardBackUrl(String idCardBackUrl) {
        this.idCardBackUrl = idCardBackUrl;
    }
    
    public String getIdCardHandUrl() {
        return idCardHandUrl;
    }
    
    public void setIdCardHandUrl(String idCardHandUrl) {
        this.idCardHandUrl = idCardHandUrl;
    }
    
    public String getNationality() {
        return nationality;
    }
    
    public void setNationality(String nationality) {
        this.nationality = nationality;
    }
    
    public String getOccupation() {
        return occupation;
    }
    
    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getApplicationType() {
        return applicationType;
    }
    
    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }
    
    public String getIncomeSource() {
        return incomeSource;
    }
    
    public void setIncomeSource(String incomeSource) {
        this.incomeSource = incomeSource;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getIdType() {
        return idType;
    }
    
    public void setIdType(String idType) {
        this.idType = idType;
    }
    
    public String getDateOfBirth() {
        return dateOfBirth;
    }
    
    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public String getPostalCode() {
        return postalCode;
    }
    
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
    
    public String getIdFrontImage() {
        return idFrontImage;
    }
    
    public void setIdFrontImage(String idFrontImage) {
        this.idFrontImage = idFrontImage;
    }
    
    public String getIdBackImage() {
        return idBackImage;
    }
    
    public void setIdBackImage(String idBackImage) {
        this.idBackImage = idBackImage;
    }
    
    public String getSelfieImage() {
        return selfieImage;
    }
    
    public void setSelfieImage(String selfieImage) {
        this.selfieImage = selfieImage;
    }
    
    public String getProofOfAddressImage() {
        return proofOfAddressImage;
    }
    
    public void setProofOfAddressImage(String proofOfAddressImage) {
        this.proofOfAddressImage = proofOfAddressImage;
    }
}