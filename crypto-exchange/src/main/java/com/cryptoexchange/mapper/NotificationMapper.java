package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.entity.Notification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 通知Mapper接口
 */
@Mapper
public interface NotificationMapper extends BaseMapper<Notification> {

    /**
     * 分页查询用户通知
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param type 通知类型
     * @param isRead 是否已读
     * @return 通知分页结果
     */
    @Select("<script>" +
            "SELECT * FROM notifications WHERE user_id = #{userId} AND is_deleted = 0" +
            "<if test='type != null and type != \"\\'> AND type = #{type}</if>" +
            "<if test='isRead != null'> AND is_read = #{isRead}</if>" +
            " ORDER BY create_time DESC" +
            "</script>")
    IPage<Notification> selectUserNotifications(Page<Notification> page, 
                                              @Param("userId") Long userId,
                                              @Param("type") String type,
                                              @Param("isRead") Integer isRead);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    @Select("SELECT COUNT(*) FROM notifications WHERE user_id = #{userId} AND is_read = 0 AND is_deleted = 0")
    Integer getUnreadCount(@Param("userId") Long userId);

    /**
     * 批量标记通知为已读
     *
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE notifications SET is_read = 1, read_time = NOW() " +
            "WHERE user_id = #{userId} AND is_deleted = 0 AND id IN" +
            "<foreach collection='notificationIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchMarkAsRead(@Param("userId") Long userId, @Param("notificationIds") List<Long> notificationIds);

    /**
     * 标记单个通知为已读
     *
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 更新行数
     */
    @Update("UPDATE notifications SET is_read = 1, read_time = NOW() " +
            "WHERE user_id = #{userId} AND id = #{notificationId} AND is_deleted = 0")
    int markAsRead(@Param("userId") Long userId, @Param("notificationId") Long notificationId);

    /**
     * 获取用户各类型未读通知数量
     *
     * @param userId 用户ID
     * @return 各类型未读通知数量
     */
    @Select("SELECT type, COUNT(*) as count FROM notifications " +
            "WHERE user_id = #{userId} AND is_read = 0 AND is_deleted = 0 " +
            "GROUP BY type")
    List<Object> getUnreadCountByType(@Param("userId") Long userId);

    /**
     * 删除过期通知
     *
     * @return 删除行数
     */
    @Update("UPDATE notifications SET is_deleted = 1 " +
            "WHERE expire_time < NOW() AND is_deleted = 0")
    int deleteExpiredNotifications();

    /**
     * 全部标记为已读
     *
     * @param userId 用户ID
     * @return 更新行数
     */
    @Update("UPDATE notifications SET is_read = 1, read_time = NOW() " +
            "WHERE user_id = #{userId} AND is_read = 0 AND is_deleted = 0")
    int markAllAsRead(@Param("userId") Long userId);
}