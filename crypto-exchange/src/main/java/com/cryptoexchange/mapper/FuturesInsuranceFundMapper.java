package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesInsuranceFund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 期货保险基金Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesInsuranceFundMapper extends BaseMapper<FuturesInsuranceFund> {

    /**
     * 根据合约符号查询保险基金
     */
    @Select("SELECT * FROM futures_insurance_fund WHERE symbol = #{symbol} AND is_deleted = 0")
    FuturesInsuranceFund findBySymbol(@Param("symbol") String symbol);

    /**
     * 获取指定合约的保险基金余额
     */
    @Select("SELECT COALESCE(balance, 0) FROM futures_insurance_fund WHERE symbol = #{symbol} AND is_deleted = 0")
    BigDecimal getBalanceBySymbol(@Param("symbol") String symbol);

    /**
     * 获取所有保险基金
     */
    @Select("SELECT * FROM futures_insurance_fund WHERE is_deleted = 0 ORDER BY symbol")
    List<FuturesInsuranceFund> getAllInsuranceFunds();

    /**
     * 获取总保险基金余额
     */
    @Select("SELECT COALESCE(SUM(balance), 0) FROM futures_insurance_fund WHERE is_deleted = 0")
    BigDecimal getTotalBalance();

    /**
     * 更新保险基金余额
     */
    @Update("UPDATE futures_insurance_fund SET balance = balance + #{amount}, " +
            "total_income = CASE WHEN #{amount} > 0 THEN total_income + #{amount} ELSE total_income END, " +
            "total_expense = CASE WHEN #{amount} < 0 THEN total_expense + ABS(#{amount}) ELSE total_expense END, " +
            "updated_at = NOW() WHERE symbol = #{symbol} AND is_deleted = 0")
    int updateBalance(@Param("symbol") String symbol, @Param("amount") BigDecimal amount);

    /**
     * 创建或更新保险基金
     */
    @Update("INSERT INTO futures_insurance_fund (symbol, asset, balance, total_income, total_expense, status, created_at, updated_at) " +
            "VALUES (#{symbol}, #{asset}, #{balance}, " +
            "CASE WHEN #{balance} > 0 THEN #{balance} ELSE 0 END, " +
            "CASE WHEN #{balance} < 0 THEN ABS(#{balance}) ELSE 0 END, " +
            "1, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "balance = balance + #{balance}, " +
            "total_income = CASE WHEN #{balance} > 0 THEN total_income + #{balance} ELSE total_income END, " +
            "total_expense = CASE WHEN #{balance} < 0 THEN total_expense + ABS(#{balance}) ELSE total_expense END, " +
            "updated_at = NOW()")
    int createOrUpdateFund(@Param("symbol") String symbol, @Param("asset") String asset, @Param("balance") BigDecimal balance);

    /**
     * 根据资产类型查询保险基金
     */
    @Select("SELECT * FROM futures_insurance_fund WHERE asset = #{asset} AND is_deleted = 0")
    List<FuturesInsuranceFund> findByAsset(@Param("asset") String asset);

    /**
     * 获取活跃的保险基金
     */
    @Select("SELECT * FROM futures_insurance_fund WHERE status = 1 AND is_deleted = 0")
    List<FuturesInsuranceFund> getActiveFunds();
}
