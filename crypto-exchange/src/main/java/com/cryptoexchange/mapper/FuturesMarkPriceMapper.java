package com.cryptoexchange.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 期货标记价格Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesMarkPriceMapper {

    /**
     * 获取最新标记价格
     */
    @Select("SELECT mark_price FROM futures_mark_price WHERE symbol = #{symbol} " +
            "ORDER BY update_time DESC LIMIT 1")
    BigDecimal getLatestMarkPrice(@Param("symbol") String symbol);

    /**
     * 获取指定时间的标记价格
     */
    @Select("SELECT mark_price FROM futures_mark_price WHERE symbol = #{symbol} " +
            "AND update_time <= #{time} ORDER BY update_time DESC LIMIT 1")
    BigDecimal getMarkPriceAtTime(@Param("symbol") String symbol, @Param("time") LocalDateTime time);

    /**
     * 更新标记价格
     */
    @Update("INSERT INTO futures_mark_price (symbol, mark_price, index_price, fair_price, update_time) " +
            "VALUES (#{symbol}, #{markPrice}, #{indexPrice}, #{fairPrice}, NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "mark_price = #{markPrice}, index_price = #{indexPrice}, fair_price = #{fairPrice}, update_time = NOW()")
    int updateMarkPrice(@Param("symbol") String symbol, 
                       @Param("markPrice") BigDecimal markPrice,
                       @Param("indexPrice") BigDecimal indexPrice,
                       @Param("fairPrice") BigDecimal fairPrice);

    /**
     * 批量更新标记价格
     */
    @Update("<script>" +
            "INSERT INTO futures_mark_price (symbol, mark_price, index_price, fair_price, update_time) VALUES " +
            "<foreach collection='priceList' item='price' separator=','>" +
            "(#{price.symbol}, #{price.markPrice}, #{price.indexPrice}, #{price.fairPrice}, NOW())" +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE " +
            "mark_price = VALUES(mark_price), index_price = VALUES(index_price), " +
            "fair_price = VALUES(fair_price), update_time = VALUES(update_time)" +
            "</script>")
    int batchUpdateMarkPrice(@Param("priceList") List<Map<String, Object>> priceList);

    /**
     * 获取所有合约的最新标记价格
     */
    @Select("SELECT symbol, mark_price, index_price, fair_price, update_time " +
            "FROM futures_mark_price WHERE (symbol, update_time) IN " +
            "(SELECT symbol, MAX(update_time) FROM futures_mark_price GROUP BY symbol)")
    List<Map<String, Object>> getAllLatestMarkPrices();

    /**
     * 获取标记价格历史
     */
    @Select("SELECT symbol, mark_price, index_price, fair_price, update_time " +
            "FROM futures_mark_price WHERE symbol = #{symbol} " +
            "AND update_time >= #{startTime} AND update_time <= #{endTime} " +
            "ORDER BY update_time DESC")
    List<Map<String, Object>> getMarkPriceHistory(@Param("symbol") String symbol,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 清理过期的标记价格数据
     */
    @Update("DELETE FROM futures_mark_price WHERE update_time < #{expireTime}")
    int cleanExpiredData(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 获取标记价格统计信息
     */
    @Select("SELECT symbol, " +
            "MIN(mark_price) as min_price, " +
            "MAX(mark_price) as max_price, " +
            "AVG(mark_price) as avg_price, " +
            "COUNT(*) as count " +
            "FROM futures_mark_price WHERE symbol = #{symbol} " +
            "AND update_time >= #{startTime} AND update_time <= #{endTime} " +
            "GROUP BY symbol")
    Map<String, Object> getMarkPriceStats(@Param("symbol") String symbol,
                                         @Param("startTime") LocalDateTime startTime,
                                         @Param("endTime") LocalDateTime endTime);
}
