package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesFundingRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货资金费率Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesFundingRateMapper extends BaseMapper<FuturesFundingRate> {

    /**
     * 查询当前资金费率
     */
    @Select("SELECT * FROM futures_funding_rate WHERE symbol = #{symbol} AND status = 1 " +
            "AND is_deleted = 0 ORDER BY funding_time DESC LIMIT 1")
    FuturesFundingRate selectCurrentBySymbol(@Param("symbol") String symbol);

    /**
     * 查询指定合约的资金费率历史
     */
    @Select("SELECT * FROM futures_funding_rate WHERE symbol = #{symbol} AND is_deleted = 0 " +
            "ORDER BY funding_time DESC LIMIT #{limit}")
    List<FuturesFundingRate> selectHistoryBySymbol(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 查询指定时间范围内的资金费率
     */
    @Select("SELECT * FROM futures_funding_rate WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<FuturesFundingRate> selectBySymbolAndTimeRange(@Param("symbol") String symbol,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询所有活跃合约的当前资金费率
     */
    @Select("SELECT * FROM futures_funding_rate WHERE status = 1 AND is_deleted = 0 " +
            "GROUP BY symbol ORDER BY funding_time DESC")
    List<FuturesFundingRate> selectAllCurrent();

    /**
     * 查询需要更新的资金费率（下次资金时间已到）
     */
    @Select("SELECT * FROM futures_funding_rate WHERE next_funding_time <= #{currentTime} " +
            "AND status = 1 AND is_deleted = 0")
    List<FuturesFundingRate> selectNeedUpdate(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询最新的资金费率记录
     */
    @Select("SELECT * FROM futures_funding_rate WHERE is_deleted = 0 " +
            "ORDER BY funding_time DESC LIMIT #{limit}")
    List<FuturesFundingRate> selectLatest(@Param("limit") Integer limit);
}
