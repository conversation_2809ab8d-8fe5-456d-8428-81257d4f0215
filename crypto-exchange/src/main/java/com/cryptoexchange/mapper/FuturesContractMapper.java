package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesContract;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 期货合约Mapper接口
 * 提供期货合约相关的数据库操作
 */
@Mapper
public interface FuturesContractMapper extends BaseMapper<FuturesContract> {

    /**
     * 根据合约符号查询
     */
    @Select("SELECT * FROM futures_contract WHERE symbol = #{symbol}")
    FuturesContract findBySymbol(@Param("symbol") String symbol);

    /**
     * 查询所有活跃合约
     */
    @Select("SELECT * FROM futures_contract WHERE status = 'ACTIVE' ORDER BY sort_order ASC, symbol ASC")
    List<FuturesContract> findActiveContracts();

    /**
     * 查询所有活跃合约（别名方法）
     */
    @Select("SELECT * FROM futures_contract WHERE status = 'ACTIVE' ORDER BY sort_order ASC, symbol ASC")
    List<FuturesContract> findAllActiveContracts();

    /**
     * 根据基础资产查询合约
     */
    @Select("SELECT * FROM futures_contract WHERE base_asset = #{baseAsset} AND status = 'ACTIVE'")
    List<FuturesContract> findByBaseAsset(@Param("baseAsset") String baseAsset);

    /**
     * 根据计价资产查询合约
     */
    @Select("SELECT * FROM futures_contract WHERE quote_asset = #{quoteAsset} AND status = 'ACTIVE'")
    List<FuturesContract> findByQuoteAsset(@Param("quoteAsset") String quoteAsset);

    /**
     * 根据合约类型查询
     */
    @Select("SELECT * FROM futures_contract WHERE contract_type = #{contractType} AND status = 'ACTIVE'")
    List<FuturesContract> findByContractType(@Param("contractType") String contractType);

    /**
     * 更新合约价格信息
     */
    @Update("UPDATE futures_contract SET mark_price = #{markPrice}, index_price = #{indexPrice}, funding_rate = #{fundingRate}, price_change_24h = #{priceChange24h}, volume_24h = #{volume24h}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updatePriceInfo(@Param("symbol") String symbol, @Param("markPrice") BigDecimal markPrice,
                       @Param("indexPrice") BigDecimal indexPrice, @Param("fundingRate") BigDecimal fundingRate,
                       @Param("priceChange24h") BigDecimal priceChange24h, @Param("volume24h") BigDecimal volume24h,
                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新合约状态
     */
    @Update("UPDATE futures_contract SET status = #{status}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateStatus(@Param("symbol") String symbol, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新资金费率
     */
    @Update("UPDATE futures_contract SET funding_rate = #{fundingRate}, next_funding_time = #{nextFundingTime}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateFundingRate(@Param("symbol") String symbol, @Param("fundingRate") BigDecimal fundingRate,
                         @Param("nextFundingTime") LocalDateTime nextFundingTime, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新最大杠杆倍数
     */
    @Update("UPDATE futures_contract SET max_leverage = #{maxLeverage}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateMaxLeverage(@Param("symbol") String symbol, @Param("maxLeverage") Integer maxLeverage, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新维持保证金率
     */
    @Update("UPDATE futures_contract SET maintenance_margin_rate = #{maintenanceMarginRate}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateMaintenanceMarginRate(@Param("symbol") String symbol, @Param("maintenanceMarginRate") BigDecimal maintenanceMarginRate, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 搜索合约
     */
    @Select("SELECT * FROM futures_contract WHERE (symbol LIKE CONCAT('%', #{keyword}, '%') OR base_asset LIKE CONCAT('%', #{keyword}, '%')) AND status = 'ACTIVE'")
    List<FuturesContract> searchContracts(@Param("keyword") String keyword);

    /**
     * 查询热门合约
     */
    @Select("SELECT * FROM futures_contract WHERE is_hot = true AND status = 'ACTIVE' ORDER BY volume_24h DESC LIMIT #{limit}")
    List<FuturesContract> findHotContracts(@Param("limit") Integer limit);

    /**
     * 查询新上线合约
     */
    @Select("SELECT * FROM futures_contract WHERE is_new = true AND status = 'ACTIVE' ORDER BY create_time DESC LIMIT #{limit}")
    List<FuturesContract> findNewContracts(@Param("limit") Integer limit);

    /**
     * 查询涨幅榜
     */
    @Select("SELECT * FROM futures_contract WHERE status = 'ACTIVE' AND price_change_24h > 0 ORDER BY price_change_24h DESC LIMIT #{limit}")
    List<FuturesContract> findTopGainers(@Param("limit") Integer limit);

    /**
     * 查询跌幅榜
     */
    @Select("SELECT * FROM futures_contract WHERE status = 'ACTIVE' AND price_change_24h < 0 ORDER BY price_change_24h ASC LIMIT #{limit}")
    List<FuturesContract> findTopLosers(@Param("limit") Integer limit);

    /**
     * 查询成交量排行
     */
    @Select("SELECT * FROM futures_contract WHERE status = 'ACTIVE' ORDER BY volume_24h DESC LIMIT #{limit}")
    List<FuturesContract> findTopVolume(@Param("limit") Integer limit);

    /**
     * 查询即将到期的合约
     */
    @Select("SELECT * FROM futures_contract WHERE contract_type = 'DELIVERY' AND delivery_time <= #{timeThreshold} AND status = 'ACTIVE' ORDER BY delivery_time ASC")
    List<FuturesContract> findExpiringContracts(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 查询合约统计信息
     */
    @Select("SELECT COUNT(*) as total_count, COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_count, COUNT(CASE WHEN contract_type = 'PERPETUAL' THEN 1 END) as perpetual_count, COUNT(CASE WHEN contract_type = 'DELIVERY' THEN 1 END) as delivery_count FROM futures_contract")
    Map<String, Object> getContractStatistics();

    /**
     * 查询需要更新资金费率的合约
     */
    @Select("SELECT * FROM futures_contract WHERE contract_type = 'PERPETUAL' AND next_funding_time <= #{currentTime} AND status = 'ACTIVE'")
    List<FuturesContract> findContractsNeedFundingUpdate(@Param("currentTime") LocalDateTime currentTime);
}