package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.entity.LoginHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录历史Mapper接口
 */
@Mapper
public interface LoginHistoryMapper extends BaseMapper<LoginHistory> {

    /**
     * 分页查询用户登录历史
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param loginStatus 登录状态
     * @return 登录历史分页结果
     */
    @Select("<script>" +
            "SELECT * FROM login_history WHERE user_id = #{userId} AND is_deleted = 0" +
            "<if test='startTime != null'> AND login_time >= #{startTime}</if>" +
            "<if test='endTime != null'> AND login_time <= #{endTime}</if>" +
            "<if test='loginStatus != null and loginStatus != \"\\'> AND login_status = #{loginStatus}</if>" +
            " ORDER BY login_time DESC" +
            "</script>")
    IPage<LoginHistory> selectUserLoginHistory(Page<LoginHistory> page,
                                             @Param("userId") Long userId,
                                             @Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("loginStatus") String loginStatus);

    /**
     * 获取用户最近的登录记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的登录记录
     */
    @Select("SELECT * FROM login_history " +
            "WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY login_time DESC LIMIT #{limit}")
    List<LoginHistory> getRecentLoginHistory(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 获取用户最后一次成功登录记录
     *
     * @param userId 用户ID
     * @return 最后一次成功登录记录
     */
    @Select("SELECT * FROM login_history " +
            "WHERE user_id = #{userId} AND login_status = 'SUCCESS' AND is_deleted = 0 " +
            "ORDER BY login_time DESC LIMIT 1")
    LoginHistory getLastSuccessfulLogin(@Param("userId") Long userId);

    /**
     * 获取用户登录失败次数（指定时间范围内）
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 登录失败次数
     */
    @Select("SELECT COUNT(*) FROM login_history " +
            "WHERE user_id = #{userId} AND login_status = 'FAILED' " +
            "AND login_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    int getFailedLoginCount(@Param("userId") Long userId,
                           @Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime);

    /**
     * 获取用户可疑登录记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 可疑登录记录
     */
    @Select("SELECT * FROM login_history " +
            "WHERE user_id = #{userId} AND is_suspicious = 1 AND is_deleted = 0 " +
            "ORDER BY login_time DESC LIMIT #{limit}")
    List<LoginHistory> getSuspiciousLogins(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 获取用户登录设备统计
     *
     * @param userId 用户ID
     * @return 设备统计信息
     */
    @Select("SELECT device_type, COUNT(*) as count FROM login_history " +
            "WHERE user_id = #{userId} AND login_status = 'SUCCESS' AND is_deleted = 0 " +
            "GROUP BY device_type")
    List<Object> getDeviceStatistics(@Param("userId") Long userId);

    /**
     * 获取用户登录地点统计
     *
     * @param userId 用户ID
     * @return 地点统计信息
     */
    @Select("SELECT location, COUNT(*) as count FROM login_history " +
            "WHERE user_id = #{userId} AND login_status = 'SUCCESS' " +
            "AND location IS NOT NULL AND location != '' AND is_deleted = 0 " +
            "GROUP BY location ORDER BY count DESC LIMIT 10")
    List<Object> getLocationStatistics(@Param("userId") Long userId);

    /**
     * 更新登出时间和会话持续时间
     *
     * @param id 登录历史ID
     * @param logoutTime 登出时间
     * @param sessionDuration 会话持续时间（分钟）
     * @return 更新行数
     */
    @Update("UPDATE login_history SET logout_time = #{logoutTime}, " +
            "session_duration = #{sessionDuration} WHERE id = #{id}")
    int updateLogoutInfo(@Param("id") Long id,
                        @Param("logoutTime") LocalDateTime logoutTime,
                        @Param("sessionDuration") Integer sessionDuration);

    /**
     * 获取用户今日登录次数
     *
     * @param userId 用户ID
     * @return 今日登录次数
     */
    @Select("SELECT COUNT(*) FROM login_history " +
            "WHERE user_id = #{userId} AND DATE(login_time) = CURDATE() " +
            "AND login_status = 'SUCCESS' AND is_deleted = 0")
    int getTodayLoginCount(@Param("userId") Long userId);

    /**
     * 获取用户登录统计信息
     *
     * @param userId 用户ID
     * @return 登录统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_logins, " +
            "SUM(CASE WHEN login_status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_logins, " +
            "SUM(CASE WHEN login_status = 'FAILED' THEN 1 ELSE 0 END) as failed_logins, " +
            "SUM(CASE WHEN is_suspicious = 1 THEN 1 ELSE 0 END) as suspicious_logins, " +
            "AVG(session_duration) as avg_session_duration " +
            "FROM login_history " +
            "WHERE user_id = #{userId} AND is_deleted = 0")
    Object getLoginStatistics(@Param("userId") Long userId);
}