package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.entity.ReferralRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 推荐记录Mapper接口
 */
@Mapper
public interface ReferralMapper extends BaseMapper<ReferralRecord> {

    /**
     * 分页查询用户的推荐记录
     *
     * @param page 分页参数
     * @param referrerUserId 推荐人用户ID
     * @param status 推荐状态
     * @return 推荐记录分页结果
     */
    @Select("<script>" +
            "SELECT rr.*, u.username as referred_username, u.email as referred_email, " +
            "u.create_time as referred_register_time FROM referral_records rr " +
            "LEFT JOIN users u ON rr.referred_user_id = u.id " +
            "WHERE rr.referrer_user_id = #{referrerUserId} AND rr.is_deleted = 0" +
            "<if test='status != null and status != \"\\'> AND rr.status = #{status}</if>" +
            " ORDER BY rr.create_time DESC" +
            "</script>")
    IPage<Object> selectReferralUsers(Page<Object> page,
                                     @Param("referrerUserId") Long referrerUserId,
                                     @Param("status") String status);

    /**
     * 根据推荐码查找推荐记录
     *
     * @param referralCode 推荐码
     * @return 推荐记录
     */
    @Select("SELECT * FROM referral_records " +
            "WHERE referral_code = #{referralCode} AND is_deleted = 0")
    ReferralRecord selectByReferralCode(@Param("referralCode") String referralCode);

    /**
     * 根据被推荐人ID查找推荐记录
     *
     * @param referredUserId 被推荐人用户ID
     * @return 推荐记录
     */
    @Select("SELECT * FROM referral_records " +
            "WHERE referred_user_id = #{referredUserId} AND is_deleted = 0")
    ReferralRecord selectByReferredUserId(@Param("referredUserId") Long referredUserId);

    /**
     * 获取用户推荐统计信息
     *
     * @param referrerUserId 推荐人用户ID
     * @return 推荐统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_referrals, " +
            "SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_referrals, " +
            "SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_referrals, " +
            "COALESCE(SUM(total_commission), 0) as total_commission_earned, " +
            "COALESCE(SUM(referred_user_volume), 0) as total_referred_volume " +
            "FROM referral_records " +
            "WHERE referrer_user_id = #{referrerUserId} AND is_deleted = 0")
    Object getReferralStatistics(@Param("referrerUserId") Long referrerUserId);

    /**
     * 获取用户最近的推荐记录
     *
     * @param referrerUserId 推荐人用户ID
     * @param limit 限制数量
     * @return 最近的推荐记录
     */
    @Select("SELECT rr.*, u.username as referred_username " +
            "FROM referral_records rr " +
            "LEFT JOIN users u ON rr.referred_user_id = u.id " +
            "WHERE rr.referrer_user_id = #{referrerUserId} AND rr.is_deleted = 0 " +
            "ORDER BY rr.create_time DESC LIMIT #{limit}")
    List<Object> getRecentReferrals(@Param("referrerUserId") Long referrerUserId, @Param("limit") int limit);

    /**
     * 更新推荐记录状态
     *
     * @param id 推荐记录ID
     * @param status 新状态
     * @param activationTime 激活时间
     * @return 更新行数
     */
    @Update("UPDATE referral_records SET status = #{status}, " +
            "activation_time = #{activationTime}, update_time = NOW() " +
            "WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, 
                    @Param("status") String status,
                    @Param("activationTime") LocalDateTime activationTime);

    /**
     * 更新推荐佣金
     *
     * @param id 推荐记录ID
     * @param commissionAmount 佣金增量
     * @return 更新行数
     */
    @Update("UPDATE referral_records SET total_commission = total_commission + #{commissionAmount}, " +
            "update_time = NOW() WHERE id = #{id}")
    int updateCommission(@Param("id") Long id, @Param("commissionAmount") BigDecimal commissionAmount);

    /**
     * 更新被推荐人交易量
     *
     * @param referredUserId 被推荐人用户ID
     * @param volumeAmount 交易量增量
     * @return 更新行数
     */
    @Update("UPDATE referral_records SET referred_user_volume = referred_user_volume + #{volumeAmount}, " +
            "update_time = NOW() WHERE referred_user_id = #{referredUserId}")
    int updateReferredUserVolume(@Param("referredUserId") Long referredUserId, 
                                @Param("volumeAmount") BigDecimal volumeAmount);

    /**
     * 获取推荐排行榜
     *
     * @param limit 限制数量
     * @return 推荐排行榜
     */
    @Select("SELECT rr.referrer_user_id, u.username, " +
            "COUNT(*) as referral_count, " +
            "SUM(rr.total_commission) as total_commission " +
            "FROM referral_records rr " +
            "LEFT JOIN users u ON rr.referrer_user_id = u.id " +
            "WHERE rr.status = 'ACTIVE' AND rr.is_deleted = 0 " +
            "GROUP BY rr.referrer_user_id " +
            "ORDER BY referral_count DESC, total_commission DESC " +
            "LIMIT #{limit}")
    List<Object> getReferralRanking(@Param("limit") Integer limit);

    /**
     * 获取用户推荐码
     *
     * @param userId 用户ID
     * @return 推荐码
     */
    @Select("SELECT referral_code FROM referral_records " +
            "WHERE referrer_user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    String getUserReferralCode(@Param("userId") Long userId);

    /**
     * 获取用户的推荐层级信息
     *
     * @param referrerUserId 推荐人用户ID
     * @return 推荐层级信息
     */
    @Select("SELECT referral_level, COUNT(*) as count, " +
            "SUM(total_commission) as level_commission " +
            "FROM referral_records " +
            "WHERE referrer_user_id = #{referrerUserId} AND status = 'ACTIVE' AND is_deleted = 0 " +
            "GROUP BY referral_level ORDER BY referral_level")
    List<Object> getReferralLevelInfo(@Param("referrerUserId") Long referrerUserId);

    /**
     * 获取月度推荐统计
     *
     * @param referrerUserId 推荐人用户ID
     * @param year 年份
     * @param month 月份
     * @return 月度推荐统计
     */
    @Select("SELECT " +
            "COUNT(*) as monthly_referrals, " +
            "SUM(total_commission) as monthly_commission " +
            "FROM referral_records " +
            "WHERE referrer_user_id = #{referrerUserId} " +
            "AND YEAR(create_time) = #{year} AND MONTH(create_time) = #{month} " +
            "AND is_deleted = 0")
    Object getMonthlyReferralStats(@Param("referrerUserId") Long referrerUserId,
                                  @Param("year") int year,
                                  @Param("month") int month);

    /**
     * 检查推荐码是否存在
     *
     * @param referralCode 推荐码
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM referral_records " +
            "WHERE referral_code = #{referralCode} AND is_deleted = 0")
    boolean existsByReferralCode(@Param("referralCode") String referralCode);
}