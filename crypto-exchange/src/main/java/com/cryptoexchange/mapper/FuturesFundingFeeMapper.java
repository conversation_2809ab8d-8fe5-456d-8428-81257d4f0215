package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesFundingFee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货资金费用记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesFundingFeeMapper extends BaseMapper<FuturesFundingFee> {

    /**
     * 查询用户资金费用记录
     */
    @Select("SELECT * FROM futures_funding_fee WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY funding_time DESC")
    List<FuturesFundingFee> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定合约的资金费用记录
     */
    @Select("SELECT * FROM futures_funding_fee WHERE user_id = #{userId} AND symbol = #{symbol} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<FuturesFundingFee> selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 分页查询用户资金费用记录
     */
    @Select("<script>" +
            "SELECT * FROM futures_funding_fee WHERE user_id = #{userId} " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "AND is_deleted = 0 ORDER BY funding_time DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<FuturesFundingFee> selectByUserId(@Param("userId") Long userId, 
                                          @Param("symbol") String symbol,
                                          @Param("offset") Integer offset, 
                                          @Param("limit") Integer limit);

    /**
     * 统计用户资金费用记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM futures_funding_fee WHERE user_id = #{userId} " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "AND is_deleted = 0" +
            "</script>")
    Long countByUserId(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询指定时间范围内的资金费用记录
     */
    @Select("SELECT * FROM futures_funding_fee WHERE user_id = #{userId} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<FuturesFundingFee> selectByUserIdAndTimeRange(@Param("userId") Long userId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户资金费用总额
     */
    @Select("SELECT COALESCE(SUM(funding_fee), 0) FROM futures_funding_fee " +
            "WHERE user_id = #{userId} AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0")
    BigDecimal sumFundingFeeByUser(@Param("userId") Long userId,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询合约的资金费用记录
     */
    @Select("SELECT * FROM futures_funding_fee WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<FuturesFundingFee> selectBySymbolAndTimeRange(@Param("symbol") String symbol,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近的资金费用记录
     */
    @Select("SELECT * FROM futures_funding_fee WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY funding_time DESC LIMIT #{limit}")
    List<FuturesFundingFee> selectRecentByUser(@Param("userId") Long userId, @Param("limit") Integer limit);
}
