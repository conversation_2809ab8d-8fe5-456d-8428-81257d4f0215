package com.cryptoexchange.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 现货账户Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface SpotAccountMapper {

    /**
     * 获取用户指定资产的余额
     */
    @Select("SELECT COALESCE(balance, 0) FROM spot_account WHERE user_id = #{userId} AND asset = #{asset} AND is_deleted = 0")
    BigDecimal getBalance(@Param("userId") Long userId, @Param("asset") String asset);

    /**
     * 更新用户资产余额
     */
    @Update("INSERT INTO spot_account (user_id, asset, balance, available_balance, frozen_balance, created_at, updated_at) " +
            "VALUES (#{userId}, #{asset}, #{balance}, #{balance}, 0, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "balance = #{balance}, available_balance = #{balance}, updated_at = NOW()")
    int updateBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("balance") BigDecimal balance);

    /**
     * 获取用户所有资产余额
     */
    @Select("SELECT asset, balance, available_balance, frozen_balance FROM spot_account " +
            "WHERE user_id = #{userId} AND balance > 0 AND is_deleted = 0")
    List<Map<String, Object>> getAllBalances(@Param("userId") Long userId);

    /**
     * 检查用户是否有足够余额
     */
    @Select("SELECT CASE WHEN COALESCE(available_balance, 0) >= #{amount} THEN 1 ELSE 0 END " +
            "FROM spot_account WHERE user_id = #{userId} AND asset = #{asset} AND is_deleted = 0")
    Integer checkSufficientBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 冻结资产
     */
    @Update("UPDATE spot_account SET " +
            "available_balance = available_balance - #{amount}, " +
            "frozen_balance = frozen_balance + #{amount}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND asset = #{asset} AND available_balance >= #{amount} AND is_deleted = 0")
    int freezeBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 解冻资产
     */
    @Update("UPDATE spot_account SET " +
            "available_balance = available_balance + #{amount}, " +
            "frozen_balance = frozen_balance - #{amount}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND asset = #{asset} AND frozen_balance >= #{amount} AND is_deleted = 0")
    int unfreezeBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 扣减冻结余额
     */
    @Update("UPDATE spot_account SET " +
            "balance = balance - #{amount}, " +
            "frozen_balance = frozen_balance - #{amount}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND asset = #{asset} AND frozen_balance >= #{amount} AND is_deleted = 0")
    int deductFrozenBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 增加余额
     */
    @Update("INSERT INTO spot_account (user_id, asset, balance, available_balance, frozen_balance, created_at, updated_at) " +
            "VALUES (#{userId}, #{asset}, #{amount}, #{amount}, 0, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "balance = balance + #{amount}, available_balance = available_balance + #{amount}, updated_at = NOW()")
    int addBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);
}
