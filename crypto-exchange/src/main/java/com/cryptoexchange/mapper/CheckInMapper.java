package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.entity.CheckInRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 签到记录Mapper接口
 */
@Mapper
public interface CheckInMapper extends BaseMapper<CheckInRecord> {

    /**
     * 分页查询用户签到记录
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @return 签到记录分页结果
     */
    @Select("SELECT * FROM check_in_records WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY check_in_date DESC")
    IPage<CheckInRecord> selectUserCheckInRecords(Page<CheckInRecord> page, @Param("userId") Long userId);

    /**
     * 检查用户今天是否已签到
     *
     * @param userId 用户ID
     * @param date 日期
     * @return 是否已签到
     */
    @Select("SELECT COUNT(*) > 0 FROM check_in_records " +
            "WHERE user_id = #{userId} AND check_in_date = #{date} AND is_deleted = 0")
    boolean hasCheckedInToday(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 获取用户连续签到天数
     *
     * @param userId 用户ID
     * @return 连续签到天数
     */
    @Select("<script>" +
            "SELECT COALESCE(MAX(continuous_days), 0) FROM check_in_records " +
            "WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY check_in_date DESC LIMIT 1" +
            "</script>")
    int getContinuousDays(@Param("userId") Long userId);

    /**
     * 获取用户最近的签到记录
     *
     * @param userId 用户ID
     * @return 最近的签到记录
     */
    @Select("SELECT * FROM check_in_records " +
            "WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY check_in_date DESC LIMIT 1")
    CheckInRecord getLatestCheckIn(@Param("userId") Long userId);

    /**
     * 获取用户本月签到次数
     *
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 本月签到次数
     */
    @Select("SELECT COUNT(*) FROM check_in_records " +
            "WHERE user_id = #{userId} AND YEAR(check_in_date) = #{year} " +
            "AND MONTH(check_in_date) = #{month} AND is_deleted = 0")
    int getMonthlyCheckInCount(@Param("userId") Long userId, 
                              @Param("year") int year, 
                              @Param("month") int month);

    /**
     * 获取用户总签到次数
     *
     * @param userId 用户ID
     * @return 总签到次数
     */
    @Select("SELECT COUNT(*) FROM check_in_records " +
            "WHERE user_id = #{userId} AND is_deleted = 0")
    int getTotalCheckInCount(@Param("userId") Long userId);

    /**
     * 获取用户签到日历（某月的签到日期列表）
     *
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 签到日期列表
     */
    @Select("SELECT check_in_date FROM check_in_records " +
            "WHERE user_id = #{userId} AND YEAR(check_in_date) = #{year} " +
            "AND MONTH(check_in_date) = #{month} AND is_deleted = 0 " +
            "ORDER BY check_in_date")
    List<LocalDate> getCheckInCalendar(@Param("userId") Long userId, 
                                      @Param("year") int year, 
                                      @Param("month") int month);

    /**
     * 获取用户最长连续签到天数
     *
     * @param userId 用户ID
     * @return 最长连续签到天数
     */
    @Select("SELECT COALESCE(MAX(continuous_days), 0) FROM check_in_records " +
            "WHERE user_id = #{userId} AND is_deleted = 0")
    int getMaxContinuousDays(@Param("userId") Long userId);

    /**
     * 获取用户签到统计信息
     *
     * @param userId 用户ID
     * @return 签到统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_days, " +
            "COALESCE(MAX(continuous_days), 0) as max_continuous, " +
            "COALESCE(SUM(reward_amount), 0) as total_rewards " +
            "FROM check_in_records " +
            "WHERE user_id = #{userId} AND is_deleted = 0")
    Object getCheckInStatistics(@Param("userId") Long userId);
}