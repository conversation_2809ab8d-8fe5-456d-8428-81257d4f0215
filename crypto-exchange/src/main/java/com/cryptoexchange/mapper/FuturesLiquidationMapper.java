package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesLiquidation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货强平记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesLiquidationMapper extends BaseMapper<FuturesLiquidation> {

    /**
     * 根据强平ID查询强平记录
     */
    @Select("SELECT * FROM futures_liquidation WHERE liquidation_id = #{liquidationId} AND is_deleted = 0")
    FuturesLiquidation selectByLiquidationId(@Param("liquidationId") String liquidationId);

    /**
     * 查询用户强平记录
     */
    @Select("SELECT * FROM futures_liquidation WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY create_time DESC")
    List<FuturesLiquidation> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定合约的强平记录
     */
    @Select("SELECT * FROM futures_liquidation WHERE user_id = #{userId} AND symbol = #{symbol} " +
            "AND is_deleted = 0 ORDER BY create_time DESC")
    List<FuturesLiquidation> selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 分页查询用户强平记录
     */
    @Select("<script>" +
            "SELECT * FROM futures_liquidation WHERE user_id = #{userId} " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "AND is_deleted = 0 ORDER BY create_time DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<FuturesLiquidation> selectByUserId(@Param("userId") Long userId, 
                                           @Param("symbol") String symbol,
                                           @Param("offset") Integer offset, 
                                           @Param("limit") Integer limit);

    /**
     * 统计用户强平记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM futures_liquidation WHERE user_id = #{userId} " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "AND is_deleted = 0" +
            "</script>")
    Long countByUserId(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询指定时间范围内的强平记录
     */
    @Select("SELECT * FROM futures_liquidation WHERE create_time >= #{startTime} AND create_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY create_time DESC")
    List<FuturesLiquidation> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询合约的强平记录
     */
    @Select("SELECT * FROM futures_liquidation WHERE symbol = #{symbol} AND is_deleted = 0 " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<FuturesLiquidation> selectBySymbol(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 查询最近的强平记录
     */
    @Select("SELECT * FROM futures_liquidation WHERE is_deleted = 0 " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<FuturesLiquidation> selectRecent(@Param("limit") Integer limit);
}
