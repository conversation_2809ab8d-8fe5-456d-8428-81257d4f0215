package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.entity.OperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 分页查询操作日志
     * 
     * @param page 分页对象
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param module 操作模块
     * @param result 操作结果
     * @param ipAddress IP地址
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    IPage<OperationLog> selectOperationLogs(Page<OperationLog> page,
                                           @Param("userId") Long userId,
                                           @Param("operationType") String operationType,
                                           @Param("module") String module,
                                           @Param("result") String result,
                                           @Param("ipAddress") String ipAddress,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID查询最近的操作日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作日志列表
     */
    @Select("SELECT * FROM operation_log WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY operation_time DESC LIMIT #{limit}")
    List<OperationLog> selectRecentLogsByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据IP地址查询操作日志
     * 
     * @param ipAddress IP地址
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    @Select("SELECT * FROM operation_log WHERE ip_address = #{ipAddress} AND operation_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY operation_time DESC")
    List<OperationLog> selectLogsByIpAndTime(@Param("ipAddress") String ipAddress, 
                                            @Param("startTime") LocalDateTime startTime, 
                                            @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户操作次数
     * 
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select("SELECT COUNT(*) FROM operation_log WHERE user_id = #{userId} AND operation_type = #{operationType} AND operation_time BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    Long countUserOperations(@Param("userId") Long userId, 
                            @Param("operationType") String operationType, 
                            @Param("startTime") LocalDateTime startTime, 
                            @Param("endTime") LocalDateTime endTime);

    /**
     * 删除过期的操作日志
     * 
     * @param beforeTime 时间点
     * @return 删除数量
     */
    @Select("UPDATE operation_log SET is_deleted = 1 WHERE operation_time < #{beforeTime} AND is_deleted = 0")
    Integer deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);
}