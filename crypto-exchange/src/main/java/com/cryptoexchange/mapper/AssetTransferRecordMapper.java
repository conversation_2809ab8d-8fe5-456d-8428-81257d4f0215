package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.AssetTransferRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产划转记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface AssetTransferRecordMapper extends BaseMapper<AssetTransferRecord> {

    /**
     * 根据用户ID查询划转记录
     */
    @Select("SELECT * FROM asset_transfer_record WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<AssetTransferRecord> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和资产类型查询划转记录
     */
    @Select("SELECT * FROM asset_transfer_record WHERE user_id = #{userId} AND asset = #{asset} " +
            "AND is_deleted = 0 ORDER BY created_at DESC")
    List<AssetTransferRecord> selectByUserIdAndAsset(@Param("userId") Long userId, @Param("asset") String asset);

    /**
     * 分页查询用户划转记录
     */
    @Select("SELECT * FROM asset_transfer_record WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY created_at DESC LIMIT #{offset}, #{limit}")
    List<AssetTransferRecord> selectByUserId(@Param("userId") Long userId, 
                                           @Param("offset") Integer offset, 
                                           @Param("limit") Integer limit);

    /**
     * 统计用户划转记录数量
     */
    @Select("SELECT COUNT(*) FROM asset_transfer_record WHERE user_id = #{userId} AND is_deleted = 0")
    Long countByUserId(@Param("userId") Long userId);

    /**
     * 根据时间范围查询划转记录
     */
    @Select("SELECT * FROM asset_transfer_record WHERE user_id = #{userId} " +
            "AND created_at >= #{startTime} AND created_at <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY created_at DESC")
    List<AssetTransferRecord> selectByUserIdAndTimeRange(@Param("userId") Long userId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近的划转记录
     */
    @Select("SELECT * FROM asset_transfer_record WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<AssetTransferRecord> selectRecentByUser(@Param("userId") Long userId, @Param("limit") Integer limit);
}
