package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.UserLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户等级Mapper接口
 */
@Mapper
public interface UserLevelMapper extends BaseMapper<UserLevel> {

    /**
     * 根据用户ID获取用户等级信息
     *
     * @param userId 用户ID
     * @return 用户等级信息
     */
    @Select("SELECT * FROM user_levels WHERE user_id = #{userId} AND is_deleted = 0")
    UserLevel selectByUserId(@Param("userId") Long userId);

    /**
     * 更新用户经验值
     *
     * @param userId 用户ID
     * @param expAmount 经验值增量
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET current_exp = current_exp + #{expAmount}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateUserExp(@Param("userId") Long userId, @Param("expAmount") BigDecimal expAmount);

    /**
     * 更新用户等级
     *
     * @param userId 用户ID
     * @param newLevel 新等级
     * @param requiredExp 升级所需经验值
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET current_level = #{newLevel}, " +
            "required_exp = #{requiredExp}, last_level_update = NOW(), " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateUserLevel(@Param("userId") Long userId, 
                       @Param("newLevel") Integer newLevel,
                       @Param("requiredExp") BigDecimal requiredExp);

    /**
     * 更新用户交易量
     *
     * @param userId 用户ID
     * @param volumeAmount 交易量增量
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET total_trading_volume = total_trading_volume + #{volumeAmount}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateTradingVolume(@Param("userId") Long userId, @Param("volumeAmount") BigDecimal volumeAmount);

    /**
     * 更新用户手续费
     *
     * @param userId 用户ID
     * @param feeAmount 手续费增量
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET total_fees_paid = total_fees_paid + #{feeAmount}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateFeesPaid(@Param("userId") Long userId, @Param("feeAmount") BigDecimal feeAmount);

    /**
     * 更新推荐用户数量
     *
     * @param userId 用户ID
     * @param increment 增量（通常为1）
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET referral_count = referral_count + #{increment}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateReferralCount(@Param("userId") Long userId, @Param("increment") Integer increment);

    /**
     * 更新平台币余额
     *
     * @param userId 用户ID
     * @param balance 新余额
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET platform_token_balance = #{balance}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updatePlatformTokenBalance(@Param("userId") Long userId, @Param("balance") BigDecimal balance);

    /**
     * 更新KYC状态
     *
     * @param userId 用户ID
     * @param kycStatus KYC状态
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET kyc_status = #{kycStatus}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateKycStatus(@Param("userId") Long userId, @Param("kycStatus") Integer kycStatus);

    /**
     * 更新账户年龄
     *
     * @param userId 用户ID
     * @param ageDays 账户年龄（天）
     * @return 更新行数
     */
    @Update("UPDATE user_levels SET account_age_days = #{ageDays}, " +
            "update_time = NOW() WHERE user_id = #{userId}")
    int updateAccountAge(@Param("userId") Long userId, @Param("ageDays") Integer ageDays);

    /**
     * 获取等级排行榜
     *
     * @param limit 限制数量
     * @return 等级排行榜
     */
    @Select("SELECT ul.*, u.username FROM user_levels ul " +
            "LEFT JOIN users u ON ul.user_id = u.id " +
            "WHERE ul.is_deleted = 0 AND u.is_deleted = 0 " +
            "ORDER BY ul.current_level DESC, ul.current_exp DESC " +
            "LIMIT #{limit}")
    List<Object> getLevelRanking(@Param("limit") Integer limit);

    /**
     * 获取用户等级分布统计
     *
     * @return 等级分布统计
     */
    @Select("SELECT current_level, COUNT(*) as count FROM user_levels " +
            "WHERE is_deleted = 0 GROUP BY current_level ORDER BY current_level")
    List<Object> getLevelDistribution();

    /**
     * 获取指定等级的用户数量
     *
     * @param level 等级
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM user_levels " +
            "WHERE current_level = #{level} AND is_deleted = 0")
    Integer getUserCountByLevel(@Param("level") Integer level);

    /**
     * 获取用户在等级排行榜中的排名
     *
     * @param userId 用户ID
     * @return 排名
     */
    @Select("SELECT COUNT(*) + 1 FROM user_levels ul1 " +
            "WHERE ul1.is_deleted = 0 AND " +
            "(ul1.current_level > (SELECT current_level FROM user_levels WHERE user_id = #{userId}) OR " +
            "(ul1.current_level = (SELECT current_level FROM user_levels WHERE user_id = #{userId}) AND " +
            "ul1.current_exp > (SELECT current_exp FROM user_levels WHERE user_id = #{userId})))")
    Integer getUserRanking(@Param("userId") Long userId);

    /**
     * 批量初始化用户等级（为新用户创建等级记录）
     *
     * @param userIds 用户ID列表
     * @return 插入行数
     */
    @Select("<script>" +
            "INSERT INTO user_levels (user_id, current_level, current_exp, required_exp, " +
            "total_trading_volume, total_fees_paid, referral_count, platform_token_balance, " +
            "kyc_status, account_age_days, create_time, update_time) VALUES " +
            "<foreach collection='userIds' item='userId' separator=','>" +
            "(#{userId}, 1, 0, 1000, 0, 0, 0, 0, 0, 0, NOW(), NOW())" +
            "</foreach>" +
            "</script>")
    int batchInitUserLevels(@Param("userIds") List<Long> userIds);
}