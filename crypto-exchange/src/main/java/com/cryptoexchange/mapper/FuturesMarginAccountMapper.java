package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesMarginAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 期货保证金账户Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesMarginAccountMapper extends BaseMapper<FuturesMarginAccount> {

    /**
     * 根据用户ID和资产类型查询保证金账户
     */
    @Select("SELECT * FROM futures_margin_account WHERE user_id = #{userId} AND asset = #{asset} AND is_deleted = 0")
    FuturesMarginAccount findByUserIdAndAsset(@Param("userId") Long userId, @Param("asset") String asset);

    /**
     * 根据用户ID查询所有保证金账户
     */
    @Select("SELECT * FROM futures_margin_account WHERE user_id = #{userId} AND is_deleted = 0")
    List<FuturesMarginAccount> findByUserId(@Param("userId") Long userId);

    /**
     * 获取用户指定资产的余额
     */
    @Select("SELECT COALESCE(balance, 0) FROM futures_margin_account WHERE user_id = #{userId} AND asset = #{asset} AND is_deleted = 0")
    BigDecimal getBalance(@Param("userId") Long userId, @Param("asset") String asset);

    /**
     * 获取用户指定资产的可用余额
     */
    @Select("SELECT COALESCE(available_balance, 0) FROM futures_margin_account WHERE user_id = #{userId} AND asset = #{asset} AND is_deleted = 0")
    BigDecimal getAvailableBalance(@Param("userId") Long userId, @Param("asset") String asset);

    /**
     * 更新用户资产余额
     */
    @Update("INSERT INTO futures_margin_account (user_id, asset, balance, available_balance, frozen_balance, created_at, updated_at) " +
            "VALUES (#{userId}, #{asset}, #{balance}, #{balance}, 0, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "balance = #{balance}, available_balance = #{balance}, updated_at = NOW()")
    int updateBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("balance") BigDecimal balance);

    /**
     * 增加余额
     */
    @Update("INSERT INTO futures_margin_account (user_id, asset, balance, available_balance, frozen_balance, created_at, updated_at) " +
            "VALUES (#{userId}, #{asset}, #{amount}, #{amount}, 0, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "balance = balance + #{amount}, available_balance = available_balance + #{amount}, updated_at = NOW()")
    int addBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 扣减余额
     */
    @Update("UPDATE futures_margin_account SET " +
            "balance = balance - #{amount}, " +
            "available_balance = available_balance - #{amount}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND asset = #{asset} AND available_balance >= #{amount} AND is_deleted = 0")
    int deductBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 冻结资产
     */
    @Update("UPDATE futures_margin_account SET " +
            "available_balance = available_balance - #{amount}, " +
            "frozen_balance = frozen_balance + #{amount}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND asset = #{asset} AND available_balance >= #{amount} AND is_deleted = 0")
    int freezeBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 解冻资产
     */
    @Update("UPDATE futures_margin_account SET " +
            "available_balance = available_balance + #{amount}, " +
            "frozen_balance = frozen_balance - #{amount}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId} AND asset = #{asset} AND frozen_balance >= #{amount} AND is_deleted = 0")
    int unfreezeBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 检查用户是否有足够余额
     */
    @Select("SELECT CASE WHEN COALESCE(available_balance, 0) >= #{amount} THEN 1 ELSE 0 END " +
            "FROM futures_margin_account WHERE user_id = #{userId} AND asset = #{asset} AND is_deleted = 0")
    Integer checkSufficientBalance(@Param("userId") Long userId, @Param("asset") String asset, @Param("amount") BigDecimal amount);

    /**
     * 统计用户总资产
     */
    @Select("SELECT COALESCE(SUM(balance), 0) FROM futures_margin_account WHERE user_id = #{userId} AND is_deleted = 0")
    BigDecimal sumTotalBalance(@Param("userId") Long userId);

    /**
     * 统计用户可用资产
     */
    @Select("SELECT COALESCE(SUM(available_balance), 0) FROM futures_margin_account WHERE user_id = #{userId} AND is_deleted = 0")
    BigDecimal sumAvailableBalance(@Param("userId") Long userId);
}
