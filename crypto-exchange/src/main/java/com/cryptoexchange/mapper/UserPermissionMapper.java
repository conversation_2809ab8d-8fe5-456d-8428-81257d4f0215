package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.UserPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户权限Mapper接口
 */
@Mapper
public interface UserPermissionMapper extends BaseMapper<UserPermission> {

    /**
     * 根据用户ID获取用户权限列表
     *
     * @param userId 用户ID
     * @return 用户权限列表
     */
    @Select("SELECT * FROM user_permissions " +
            "WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY permission_type")
    List<UserPermission> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和权限类型获取权限
     *
     * @param userId 用户ID
     * @param permissionType 权限类型
     * @return 用户权限
     */
    @Select("SELECT * FROM user_permissions " +
            "WHERE user_id = #{userId} AND permission_type = #{permissionType} " +
            "AND is_deleted = 0")
    UserPermission selectByUserIdAndType(@Param("userId") Long userId, 
                                        @Param("permissionType") String permissionType);

    /**
     * 获取用户有效权限列表（未过期且启用的）
     *
     * @param userId 用户ID
     * @return 有效权限列表
     */
    @Select("SELECT * FROM user_permissions " +
            "WHERE user_id = #{userId} AND status = 'ENABLED' " +
            "AND (expiry_time IS NULL OR expiry_time > NOW()) " +
            "AND (effective_time IS NULL OR effective_time <= NOW()) " +
            "AND is_deleted = 0 " +
            "ORDER BY permission_type")
    List<UserPermission> selectActivePermissions(@Param("userId") Long userId);

    /**
     * 更新权限状态
     *
     * @param id 权限ID
     * @param status 新状态
     * @return 更新行数
     */
    @Update("UPDATE user_permissions SET status = #{status}, update_time = NOW() " +
            "WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新交易限额
     *
     * @param userId 用户ID
     * @param permissionType 权限类型
     * @param dailyLimit 每日限额
     * @param singleLimit 单笔限额
     * @return 更新行数
     */
    @Update("UPDATE user_permissions SET " +
            "daily_trading_limit = #{dailyLimit}, " +
            "single_trade_limit = #{singleLimit}, " +
            "update_time = NOW() " +
            "WHERE user_id = #{userId} AND permission_type = #{permissionType}")
    int updateTradingLimits(@Param("userId") Long userId,
                           @Param("permissionType") String permissionType,
                           @Param("dailyLimit") BigDecimal dailyLimit,
                           @Param("singleLimit") BigDecimal singleLimit);

    /**
     * 更新提现限额
     *
     * @param userId 用户ID
     * @param dailyLimit 每日提现限额
     * @param singleLimit 单笔提现限额
     * @return 更新行数
     */
    @Update("UPDATE user_permissions SET " +
            "daily_withdrawal_limit = #{dailyLimit}, " +
            "single_withdrawal_limit = #{singleLimit}, " +
            "update_time = NOW() " +
            "WHERE user_id = #{userId} AND permission_type = 'WITHDRAWAL'")
    int updateWithdrawalLimits(@Param("userId") Long userId,
                              @Param("dailyLimit") BigDecimal dailyLimit,
                              @Param("singleLimit") BigDecimal singleLimit);

    /**
     * 更新API权限
     *
     * @param userId 用户ID
     * @param rateLimit API调用频率限制
     * @return 更新行数
     */
    @Update("UPDATE user_permissions SET " +
            "api_rate_limit = #{rateLimit}, " +
            "update_time = NOW() " +
            "WHERE user_id = #{userId} AND permission_type = 'API'")
    int updateApiPermission(@Param("userId") Long userId, @Param("rateLimit") Integer rateLimit);

    /**
     * 更新合约权限
     *
     * @param userId 用户ID
     * @param maxLeverage 最大杠杆倍数
     * @return 更新行数
     */
    @Update("UPDATE user_permissions SET " +
            "max_leverage = #{maxLeverage}, " +
            "update_time = NOW() " +
            "WHERE user_id = #{userId} AND permission_type = 'FUTURES'")
    int updateFuturesPermission(@Param("userId") Long userId, @Param("maxLeverage") Integer maxLeverage);

    /**
     * 批量更新权限过期时间
     *
     * @param userIds 用户ID列表
     * @param permissionType 权限类型
     * @param expiryTime 过期时间
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE user_permissions SET expiry_time = #{expiryTime}, update_time = NOW() " +
            "WHERE permission_type = #{permissionType} AND user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateExpiryTime(@Param("userIds") List<Long> userIds,
                             @Param("permissionType") String permissionType,
                             @Param("expiryTime") LocalDateTime expiryTime);

    /**
     * 获取即将过期的权限
     *
     * @param hours 小时数（多少小时内过期）
     * @return 即将过期的权限列表
     */
    @Select("SELECT up.*, u.username, u.email FROM user_permissions up " +
            "LEFT JOIN users u ON up.user_id = u.id " +
            "WHERE up.expiry_time IS NOT NULL " +
            "AND up.expiry_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{hours} HOUR) " +
            "AND up.status = 'ENABLED' AND up.is_deleted = 0")
    List<Object> getExpiringPermissions(@Param("hours") int hours);

    /**
     * 自动禁用过期权限
     *
     * @return 更新行数
     */
    @Update("UPDATE user_permissions SET status = 'DISABLED', update_time = NOW() " +
            "WHERE expiry_time < NOW() AND status = 'ENABLED' AND is_deleted = 0")
    int disableExpiredPermissions();

    /**
     * 获取权限统计信息
     *
     * @return 权限统计信息
     */
    @Select("SELECT " +
            "permission_type, " +
            "status, " +
            "COUNT(*) as count " +
            "FROM user_permissions " +
            "WHERE is_deleted = 0 " +
            "GROUP BY permission_type, status " +
            "ORDER BY permission_type, status")
    List<Object> getPermissionStatistics();

    /**
     * 检查用户是否有指定权限
     *
     * @param userId 用户ID
     * @param permissionType 权限类型
     * @return 是否有权限
     */
    @Select("SELECT COUNT(*) > 0 FROM user_permissions " +
            "WHERE user_id = #{userId} AND permission_type = #{permissionType} " +
            "AND status = 'ENABLED' " +
            "AND (expiry_time IS NULL OR expiry_time > NOW()) " +
            "AND (effective_time IS NULL OR effective_time <= NOW()) " +
            "AND is_deleted = 0")
    boolean hasPermission(@Param("userId") Long userId, @Param("permissionType") String permissionType);

    /**
     * 获取用户权限摘要
     *
     * @param userId 用户ID
     * @return 权限摘要
     */
    @Select("SELECT " +
            "COUNT(*) as total_permissions, " +
            "SUM(CASE WHEN status = 'ENABLED' THEN 1 ELSE 0 END) as enabled_permissions, " +
            "SUM(CASE WHEN status = 'DISABLED' THEN 1 ELSE 0 END) as disabled_permissions, " +
            "SUM(CASE WHEN status = 'SUSPENDED' THEN 1 ELSE 0 END) as suspended_permissions " +
            "FROM user_permissions " +
            "WHERE user_id = #{userId} AND is_deleted = 0")
    Object getPermissionSummary(@Param("userId") Long userId);
}