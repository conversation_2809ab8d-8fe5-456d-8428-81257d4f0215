package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesKline;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货K线数据Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesKlineMapper extends BaseMapper<FuturesKline> {

    /**
     * 根据合约符号和时间间隔查询K线数据
     */
    @Select("SELECT * FROM futures_kline WHERE symbol = #{symbol} AND interval = #{interval} " +
            "AND is_deleted = 0 ORDER BY open_time DESC LIMIT #{limit}")
    List<FuturesKline> selectBySymbolAndInterval(@Param("symbol") String symbol, 
                                                @Param("interval") String interval, 
                                                @Param("limit") Integer limit);

    /**
     * 根据时间范围查询K线数据
     */
    @Select("SELECT * FROM futures_kline WHERE symbol = #{symbol} AND interval = #{interval} " +
            "AND open_time >= #{startTime} AND open_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY open_time ASC")
    List<FuturesKline> selectByTimeRange(@Param("symbol") String symbol,
                                        @Param("interval") String interval,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取最新的K线数据
     */
    @Select("SELECT * FROM futures_kline WHERE symbol = #{symbol} AND interval = #{interval} " +
            "AND is_deleted = 0 ORDER BY open_time DESC LIMIT 1")
    FuturesKline selectLatest(@Param("symbol") String symbol, @Param("interval") String interval);

    /**
     * 获取指定数量的历史K线数据
     */
    @Select("SELECT * FROM futures_kline WHERE symbol = #{symbol} AND interval = #{interval} " +
            "AND is_deleted = 0 ORDER BY open_time DESC LIMIT #{limit}")
    List<FuturesKline> selectHistoryKlines(@Param("symbol") String symbol,
                                          @Param("interval") String interval,
                                          @Param("limit") Integer limit);

    /**
     * 删除过期的K线数据
     */
    @Select("DELETE FROM futures_kline WHERE open_time < #{expireTime}")
    int deleteExpiredKlines(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 统计K线数据数量
     */
    @Select("SELECT COUNT(*) FROM futures_kline WHERE symbol = #{symbol} AND interval = #{interval} " +
            "AND is_deleted = 0")
    Long countBySymbolAndInterval(@Param("symbol") String symbol, @Param("interval") String interval);

    /**
     * 获取所有支持的合约符号
     */
    @Select("SELECT DISTINCT symbol FROM futures_kline WHERE is_deleted = 0")
    List<String> selectAllSymbols();

    /**
     * 获取所有支持的时间间隔
     */
    @Select("SELECT DISTINCT interval FROM futures_kline WHERE is_deleted = 0")
    List<String> selectAllIntervals();
}
