package com.cryptoexchange.mapper;

import com.cryptoexchange.entity.FuturesOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 期货订单Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface FuturesOrderMapper extends BaseMapper<FuturesOrder> {

    /**
     * 根据用户ID分页查询期货订单
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param symbol 交易对
     * @param status 订单状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 期货订单分页
     */
    @Select("<script>" +
            "SELECT * FROM futures_order WHERE user_id = #{userId} " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "<if test='status != null and status != \"\"'>AND status = #{status}</if> " +
            "<if test='startTime != null'>AND create_time &gt;= #{startTime}</if> " +
            "<if test='endTime != null'>AND create_time &lt;= #{endTime}</if> " +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<FuturesOrder> selectPageByUserId(Page<FuturesOrder> page, 
                                          @Param("userId") Long userId,
                                          @Param("symbol") String symbol,
                                          @Param("status") String status,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 根据订单ID和用户ID查询订单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 期货订单
     */
    @Select("SELECT * FROM futures_order WHERE id = #{orderId} AND user_id = #{userId}")
    FuturesOrder selectByOrderIdAndUserId(@Param("orderId") Long orderId, @Param("userId") Long userId);

    /**
     * 查询用户未完成的订单
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @return 期货订单列表
     */
    @Select("<script>" +
            "SELECT * FROM futures_order WHERE user_id = #{userId} " +
            "AND status IN ('PENDING', 'PARTIAL_FILLED') " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "ORDER BY create_time DESC" +
            "</script>")
    List<FuturesOrder> selectActiveOrdersByUserId(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 状态
     * @param filledQuantity 已成交数量
     * @param avgPrice 平均价格
     * @return 更新行数
     */
    @Update("UPDATE futures_order SET status = #{status}, filled_quantity = #{filledQuantity}, " +
            "avg_price = #{avgPrice}, update_time = NOW() WHERE id = #{orderId}")
    int updateOrderStatus(@Param("orderId") Long orderId, 
                         @Param("status") String status,
                         @Param("filledQuantity") BigDecimal filledQuantity,
                         @Param("avgPrice") BigDecimal avgPrice);

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_orders, " +
            "SUM(CASE WHEN status = 'FILLED' THEN 1 ELSE 0 END) as filled_orders, " +
            "SUM(CASE WHEN status = 'CANCELLED' THEN 1 ELSE 0 END) as cancelled_orders, " +
            "SUM(CASE WHEN status IN ('PENDING', 'PARTIAL_FILLED') THEN 1 ELSE 0 END) as active_orders, " +
            "SUM(CASE WHEN status = 'FILLED' THEN quantity * price ELSE 0 END) as total_volume, " +
            "SUM(CASE WHEN status = 'FILLED' THEN fee ELSE 0 END) as total_fee " +
            "FROM futures_order WHERE user_id = #{userId} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime}")
    Map<String, Object> selectOrderStatistics(@Param("userId") Long userId,
                                             @Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户持仓订单
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @return 期货订单列表
     */
    @Select("<script>" +
            "SELECT * FROM futures_order WHERE user_id = #{userId} " +
            "AND position_side IS NOT NULL AND status = 'FILLED' " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "ORDER BY create_time DESC" +
            "</script>")
    List<FuturesOrder> selectPositionOrdersByUserId(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 批量取消订单
     * 
     * @param userId 用户ID
     * @param orderIds 订单ID列表
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE futures_order SET status = 'CANCELLED', update_time = NOW() " +
            "WHERE user_id = #{userId} AND status IN ('PENDING', 'PARTIAL_FILLED') " +
            "<if test='orderIds != null and orderIds.size() &gt; 0'>" +
            "AND id IN " +
            "<foreach collection='orderIds' item='orderId' open='(' separator=',' close=')'>" +
            "#{orderId}" +
            "</foreach>" +
            "</if>" +
            "</script>")
    int batchCancelOrders(@Param("userId") Long userId, @Param("orderIds") List<Long> orderIds);

    /**
     * 查询最近的订单
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 期货订单列表
     */
    @Select("SELECT * FROM futures_order WHERE user_id = #{userId} " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<FuturesOrder> selectRecentOrders(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询订单总数
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 订单总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM futures_order WHERE user_id = #{userId} " +
            "<if test='status != null and status != \"\"'>AND status = #{status}</if>" +
            "</script>")
    Long countOrdersByUserId(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据订单ID查询订单
     */
    @Select("SELECT * FROM futures_order WHERE order_id = #{orderId} AND is_deleted = 0")
    FuturesOrder selectByOrderId(@Param("orderId") String orderId);

    /**
     * 查询用户未成交订单
     */
    @Select("<script>" +
            "SELECT * FROM futures_order WHERE user_id = #{userId} " +
            "AND status IN ('PENDING', 'PARTIAL_FILLED') " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "AND is_deleted = 0 ORDER BY create_time DESC" +
            "</script>")
    List<FuturesOrder> selectPendingOrdersByUserAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 根据条件查询订单
     */
    @Select("<script>" +
            "SELECT * FROM futures_order WHERE 1=1 " +
            "<if test='userId != null'>AND user_id = #{userId}</if> " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "<if test='status != null'>AND status = #{status}</if> " +
            "AND is_deleted = 0 ORDER BY create_time DESC " +
            "LIMIT #{offset}, #{limit}" +
            "</script>")
    List<FuturesOrder> selectOrdersByCondition(Map<String, Object> params);

    /**
     * 根据条件统计订单数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM futures_order WHERE 1=1 " +
            "<if test='userId != null'>AND user_id = #{userId}</if> " +
            "<if test='symbol != null and symbol != \"\"'>AND symbol = #{symbol}</if> " +
            "<if test='status != null'>AND status = #{status}</if> " +
            "AND is_deleted = 0" +
            "</script>")
    Long countOrdersByCondition(Map<String, Object> params);

    /**
     * 查询用户交易量排行
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 交易量排行
     */
    @Select("SELECT user_id, SUM(quantity * price) as total_volume " +
            "FROM futures_order WHERE status = 'FILLED' " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} " +
            "GROUP BY user_id ORDER BY total_volume DESC LIMIT #{limit}")
    List<Map<String, Object>> selectVolumeRanking(@Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime,
                                                 @Param("limit") Integer limit);

    /**
     * 查询系统订单统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_orders, " +
            "COUNT(DISTINCT user_id) as total_users, " +
            "SUM(CASE WHEN status = 'FILLED' THEN quantity * price ELSE 0 END) as total_volume, " +
            "SUM(CASE WHEN status = 'FILLED' THEN fee ELSE 0 END) as total_fee, " +
            "AVG(CASE WHEN status = 'FILLED' THEN price ELSE NULL END) as avg_price " +
            "FROM futures_order WHERE create_time >= #{startTime} AND create_time <= #{endTime}")
    Map<String, Object> selectSystemStatistics(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);
}