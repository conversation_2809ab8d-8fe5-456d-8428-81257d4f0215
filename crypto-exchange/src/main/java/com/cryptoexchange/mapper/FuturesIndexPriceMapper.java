package com.cryptoexchange.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 期货指数价格Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesIndexPriceMapper {

    /**
     * 获取最新指数价格
     */
    @Select("SELECT index_price FROM futures_index_price WHERE symbol = #{symbol} " +
            "ORDER BY update_time DESC LIMIT 1")
    BigDecimal getLatestIndexPrice(@Param("symbol") String symbol);

    /**
     * 获取指定时间的指数价格
     */
    @Select("SELECT index_price FROM futures_index_price WHERE symbol = #{symbol} " +
            "AND update_time <= #{time} ORDER BY update_time DESC LIMIT 1")
    BigDecimal getIndexPriceAtTime(@Param("symbol") String symbol, @Param("time") LocalDateTime time);

    /**
     * 更新指数价格
     */
    @Update("INSERT INTO futures_index_price (symbol, index_price, update_time) " +
            "VALUES (#{symbol}, #{indexPrice}, NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "index_price = #{indexPrice}, update_time = NOW()")
    int updateIndexPrice(@Param("symbol") String symbol, @Param("indexPrice") BigDecimal indexPrice);

    /**
     * 批量更新指数价格
     */
    @Update("<script>" +
            "INSERT INTO futures_index_price (symbol, index_price, update_time) VALUES " +
            "<foreach collection='priceList' item='price' separator=','>" +
            "(#{price.symbol}, #{price.indexPrice}, NOW())" +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE " +
            "index_price = VALUES(index_price), update_time = VALUES(update_time)" +
            "</script>")
    int batchUpdateIndexPrice(@Param("priceList") List<Map<String, Object>> priceList);

    /**
     * 获取所有合约的最新指数价格
     */
    @Select("SELECT symbol, index_price, update_time " +
            "FROM futures_index_price WHERE (symbol, update_time) IN " +
            "(SELECT symbol, MAX(update_time) FROM futures_index_price GROUP BY symbol)")
    List<Map<String, Object>> getAllLatestIndexPrices();

    /**
     * 获取指数价格历史
     */
    @Select("SELECT symbol, index_price, update_time " +
            "FROM futures_index_price WHERE symbol = #{symbol} " +
            "AND update_time >= #{startTime} AND update_time <= #{endTime} " +
            "ORDER BY update_time DESC")
    List<Map<String, Object>> getIndexPriceHistory(@Param("symbol") String symbol,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 清理过期的指数价格数据
     */
    @Update("DELETE FROM futures_index_price WHERE update_time < #{expireTime}")
    int cleanExpiredData(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 获取指数价格统计信息
     */
    @Select("SELECT symbol, " +
            "MIN(index_price) as min_price, " +
            "MAX(index_price) as max_price, " +
            "AVG(index_price) as avg_price, " +
            "COUNT(*) as count " +
            "FROM futures_index_price WHERE symbol = #{symbol} " +
            "AND update_time >= #{startTime} AND update_time <= #{endTime} " +
            "GROUP BY symbol")
    Map<String, Object> getIndexPriceStats(@Param("symbol") String symbol,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);
}
