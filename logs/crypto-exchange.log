2025-06-03 15:11:59.040 [restartedMain] INFO  com.cryptoexchange.CryptoExchangeApplication - Starting CryptoExchangeApplication using Java 17.0.14 with PID 44604 (D:\items\icons\backend\crypto-exchange\target\classes started by Administrator in D:\items\icons\backend)
2025-06-03 15:11:59.041 [restartedMain] DEBUG com.cryptoexchange.CryptoExchangeApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-03 15:11:59.041 [restartedMain] INFO  com.cryptoexchange.CryptoExchangeApplication - The following 1 profile is active: "dev"
2025-06-03 15:11:59.078 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-03 15:11:59.079 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-03 15:12:08.228 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 15:12:08.230 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 15:12:08.260 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\CurrencyMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\DepositMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FundingRateMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesContractMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesOrderMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesPositionMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesTradeMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\InsuranceFundMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\LiquidationRecordMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\MarginAccountMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\OrderMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\TradeMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\TradingPairMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\TransactionRecordMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\UserFundingFeeMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\UserMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\UserWalletMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\WithdrawMapper.class]
2025-06-03 15:12:08.313 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'currencyMapper' and 'com.cryptoexchange.mapper.CurrencyMapper' mapperInterface
2025-06-03 15:12:08.314 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'currencyMapper'.
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'depositMapper' and 'com.cryptoexchange.mapper.DepositMapper' mapperInterface
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'depositMapper'.
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'fundingRateMapper' and 'com.cryptoexchange.mapper.FundingRateMapper' mapperInterface
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'fundingRateMapper'.
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresContractMapper' and 'com.cryptoexchange.mapper.FuturesContractMapper' mapperInterface
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresContractMapper'.
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresOrderMapper' and 'com.cryptoexchange.mapper.FuturesOrderMapper' mapperInterface
2025-06-03 15:12:08.315 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresOrderMapper'.
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresPositionMapper' and 'com.cryptoexchange.mapper.FuturesPositionMapper' mapperInterface
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresPositionMapper'.
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresTradeMapper' and 'com.cryptoexchange.mapper.FuturesTradeMapper' mapperInterface
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresTradeMapper'.
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'insuranceFundMapper' and 'com.cryptoexchange.mapper.InsuranceFundMapper' mapperInterface
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'insuranceFundMapper'.
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'liquidationRecordMapper' and 'com.cryptoexchange.mapper.LiquidationRecordMapper' mapperInterface
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'liquidationRecordMapper'.
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'marginAccountMapper' and 'com.cryptoexchange.mapper.MarginAccountMapper' mapperInterface
2025-06-03 15:12:08.316 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'marginAccountMapper'.
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'orderMapper' and 'com.cryptoexchange.mapper.OrderMapper' mapperInterface
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'orderMapper'.
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tradeMapper' and 'com.cryptoexchange.mapper.TradeMapper' mapperInterface
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'tradeMapper'.
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tradingPairMapper' and 'com.cryptoexchange.mapper.TradingPairMapper' mapperInterface
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'tradingPairMapper'.
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'transactionRecordMapper' and 'com.cryptoexchange.mapper.TransactionRecordMapper' mapperInterface
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'transactionRecordMapper'.
2025-06-03 15:12:08.317 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userFundingFeeMapper' and 'com.cryptoexchange.mapper.UserFundingFeeMapper' mapperInterface
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userFundingFeeMapper'.
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.cryptoexchange.mapper.UserMapper' mapperInterface
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userWalletMapper' and 'com.cryptoexchange.mapper.UserWalletMapper' mapperInterface
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userWalletMapper'.
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'withdrawMapper' and 'com.cryptoexchange.mapper.WithdrawMapper' mapperInterface
2025-06-03 15:12:08.318 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'withdrawMapper'.
2025-06-03 15:12:08.319 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
2025-06-03 15:12:08.328 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-03 15:12:08.339 [restartedMain] ERROR org.springframework.boot.SpringApplication - Application run failed
java.lang.IllegalArgumentException: Invalid value type for attribute 'factoryBeanObjectType': java.lang.String
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getTypeForFactoryBeanFromAttributes(FactoryBeanRegistrySupport.java:86)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryBean(AbstractAutowireCapableBeanFactory.java:838)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isTypeMatch(AbstractBeanFactory.java:620)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:573)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:532)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:138)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:775)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:597)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.cryptoexchange.CryptoExchangeApplication.main(CryptoExchangeApplication.java:24)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-06-03 16:08:47.318 [restartedMain] INFO  com.cryptoexchange.CryptoExchangeApplication - Starting CryptoExchangeApplication using Java 17.0.14 with PID 22748 (D:\items\icons\backend\crypto-exchange\target\classes started by Administrator in D:\items\icons\backend)
2025-06-03 16:08:47.319 [restartedMain] DEBUG com.cryptoexchange.CryptoExchangeApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-06-03 16:08:47.320 [restartedMain] INFO  com.cryptoexchange.CryptoExchangeApplication - The following 1 profile is active: "dev"
2025-06-03 16:08:47.354 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-03 16:08:47.354 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-03 16:08:48.165 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-03 16:08:48.166 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-03 16:08:48.197 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\CurrencyMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\DepositMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FundingRateMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesContractMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesOrderMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesPositionMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\FuturesTradeMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\InsuranceFundMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\LiquidationRecordMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\MarginAccountMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\OrderMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\TradeMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\TradingPairMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\TransactionRecordMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\UserFundingFeeMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\UserMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\UserWalletMapper.class]
2025-06-03 16:08:48.250 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\mapper\WithdrawMapper.class]
2025-06-03 16:08:48.251 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'currencyMapper' and 'com.cryptoexchange.mapper.CurrencyMapper' mapperInterface
2025-06-03 16:08:48.252 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'currencyMapper'.
2025-06-03 16:08:48.252 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'depositMapper' and 'com.cryptoexchange.mapper.DepositMapper' mapperInterface
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'depositMapper'.
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'fundingRateMapper' and 'com.cryptoexchange.mapper.FundingRateMapper' mapperInterface
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'fundingRateMapper'.
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresContractMapper' and 'com.cryptoexchange.mapper.FuturesContractMapper' mapperInterface
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresContractMapper'.
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresOrderMapper' and 'com.cryptoexchange.mapper.FuturesOrderMapper' mapperInterface
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresOrderMapper'.
2025-06-03 16:08:48.253 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresPositionMapper' and 'com.cryptoexchange.mapper.FuturesPositionMapper' mapperInterface
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresPositionMapper'.
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'futuresTradeMapper' and 'com.cryptoexchange.mapper.FuturesTradeMapper' mapperInterface
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'futuresTradeMapper'.
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'insuranceFundMapper' and 'com.cryptoexchange.mapper.InsuranceFundMapper' mapperInterface
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'insuranceFundMapper'.
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'liquidationRecordMapper' and 'com.cryptoexchange.mapper.LiquidationRecordMapper' mapperInterface
2025-06-03 16:08:48.254 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'liquidationRecordMapper'.
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'marginAccountMapper' and 'com.cryptoexchange.mapper.MarginAccountMapper' mapperInterface
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'marginAccountMapper'.
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'orderMapper' and 'com.cryptoexchange.mapper.OrderMapper' mapperInterface
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'orderMapper'.
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tradeMapper' and 'com.cryptoexchange.mapper.TradeMapper' mapperInterface
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'tradeMapper'.
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tradingPairMapper' and 'com.cryptoexchange.mapper.TradingPairMapper' mapperInterface
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'tradingPairMapper'.
2025-06-03 16:08:48.255 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'transactionRecordMapper' and 'com.cryptoexchange.mapper.TransactionRecordMapper' mapperInterface
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'transactionRecordMapper'.
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userFundingFeeMapper' and 'com.cryptoexchange.mapper.UserFundingFeeMapper' mapperInterface
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userFundingFeeMapper'.
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.cryptoexchange.mapper.UserMapper' mapperInterface
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userWalletMapper' and 'com.cryptoexchange.mapper.UserWalletMapper' mapperInterface
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userWalletMapper'.
2025-06-03 16:08:48.256 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'withdrawMapper' and 'com.cryptoexchange.mapper.WithdrawMapper' mapperInterface
2025-06-03 16:08:48.257 [restartedMain] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'withdrawMapper'.
2025-06-03 16:08:48.706 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-03 16:08:48.711 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-03 16:08:48.712 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-06-03 16:08:48.749 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-06-03 16:08:48.749 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1395 ms
2025-06-03 16:08:48.972 [restartedMain] DEBUG c.cryptoexchange.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-03 16:08:49.272 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'marketController' defined in file [D:\items\icons\backend\crypto-exchange\target\classes\com\cryptoexchange\controller\MarketController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.cryptoexchange.service.MarketService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-03 16:08:49.272 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
2025-06-03 16:08:49.278 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-03 16:08:49.288 [restartedMain] WARN  org.apache.catalina.loader.WebappClassLoaderBase - The web application [api] appears to have started a thread named [Thread-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.14/sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 java.base@17.0.14/sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:176)
2025-06-03 16:08:49.297 [restartedMain] INFO  o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-03 16:08:49.308 [restartedMain] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.cryptoexchange.controller.MarketController required a bean of type 'com.cryptoexchange.service.MarketService' that could not be found.


Action:

Consider defining a bean of type 'com.cryptoexchange.service.MarketService' in your configuration.

